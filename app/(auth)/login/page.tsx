import { Suspense } from "react"
import { UserAuthForm } from "@/components/dashboard/user-auth-form"

export default function LoginPage() {
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-bold">Welcome back</h1>
          <p className="text-sm text-slate-500">
            Enter your email to sign in to your account
          </p>
        </div>
        <Suspense fallback={<div>Loading...</div>}>
          <UserAuthForm />
        </Suspense>
      </div>
    </div>
  )
}
