import { redirect } from "next/navigation"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"
import { User } from "@prisma/client"
import { Icons } from "@/components/icons"

import { BigQuery } from "@google-cloud/bigquery"

export const revalidate = 60

async function runQuery(clientId: User["clientId"], datachimpId) {
  if (!isValidUUID(datachimpId)) throw new Error("Datachimp id is not valid!")
  if (!process.env.GOOGLE_SERVICE_KEY) {
    throw new Error(
      "GOOGLE_SERVICE_KEY is not defined in the environment variables"
    )
  }

  const credential = JSON.parse(
    Buffer.from(process.env.GOOGLE_SERVICE_KEY, "base64").toString()
  )

  const bigqueryClient = new BigQuery({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: {
      client_email: credential.client_email,
      private_key: credential.private_key,
    },
  })

  const query = `
    SELECT
    title,
    company,
    description,
    location,
    job_boards,
    created_at
    FROM \`datachimp.sources.google_job_offers_uniques\`
    WHERE datachimp_id = @datachimpId

    UNION ALL

    SELECT
    title,
    company,
    description,
    location,
    job_boards,
    created_at
    FROM \`datachimp.sources.google_job_offers_uniques_new\`
    WHERE datachimp_id = @datachimpId
    `

  const options = {
    query: query,
    params: {
      datachimpId: datachimpId,
    },
    location: "europe-west1",
  }

  const [job] = await bigqueryClient.createQueryJob(options)
  const [rows] = await job.getQueryResults()

  return rows[0]
}

function isValidUUID(uuid: string): boolean {
  const regex =
    /^[0-9a-fA-F]{8}\-[0-9a-fA-F]{4}\-4[0-9a-fA-F]{3}\-(8|9|a|b|A|B)[0-9a-fA-F]{3}\-[0-9a-fA-F]{12}$/i

  return regex.test(uuid)
}

export default async function JobOfferPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const datachimpId = searchParams.datachimp_id
  const jobOffer = await runQuery(user.clientId, datachimpId)

  return (
    <div className="pt-16 md:max-w-4xl">
      <h1 className="text-2xl font-bold">{jobOffer.title}</h1>
      <div className="my-16 space-y-2 text-sm text-slate-600">
        <div className="flex items-center">
          <Icons.building className="mr-2 h-4 w-4" />
          <p className="font-semibold">{jobOffer.company}</p>
        </div>
        <div className="flex items-center">
          <Icons.mapPin className="mr-2 h-4 w-4" />
          <p className="font-semibold">{jobOffer.location}</p>
        </div>
        <div className="flex items-center">
          <Icons.clock className="mr-2 h-4 w-4" />
          <p className="font-semibold">
            {jobOffer.created_at.value.split("T")[0]}
          </p>
        </div>
        <div className="flex items-center">
          <Icons.jobOffers className="mr-2 h-4 w-4" />
          <a href={JSON.parse(jobOffer.job_boards)[0]} target="_blank">
            <p className="font-bold underline">Source</p>
          </a>
        </div>
      </div>
      <p
        className="mb-32"
        dangerouslySetInnerHTML={{
          __html: jobOffer.description.replace(/\n/g, "<br />"),
        }}
      />
    </div>
  )
}
