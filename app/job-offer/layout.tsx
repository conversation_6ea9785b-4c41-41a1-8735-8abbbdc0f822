import { notFound } from "next/navigation"

import { getCurrentUser } from "@/lib/session"
import { UserAccountNav } from "@/components/dashboard/user-account-nav"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default async function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const user = await getCurrentUser()

  if (!user) {
    return notFound()
  }

  return (
    <div className="mx-auto flex flex-col space-y-6">
      <header className="sticky top-0 z-40 bg-white px-64">
        <div className="flex h-16 items-center justify-end py-4">
          <UserAccountNav
            user={{
              name: user.name,
              image: user.image,
              email: user.email,
            }}
          />
        </div>
      </header>
      <div className="px-64">
        <main>{children}</main>
      </div>
    </div>
  )
}
