import { redirect } from "next/navigation"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"

export const revalidate = 60

async function getTime() {
  const res = await fetch("http://worldtimeapi.org/api/timezone/Europe/Paris", {
    next: { revalidate: 60 },
  })

  return res.json()
}

export default async function NewHiredWorkflowPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const [time] = await Promise.all([getTime()])

  return (
    <div className="md:max-w-4xl">
      <h1>{time.datetime}</h1>
    </div>
  )
}
