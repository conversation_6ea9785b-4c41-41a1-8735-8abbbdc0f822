import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { NewsForm } from "@/components/dashboard/news/news-form"

import { db } from "@/lib/db"
import { authOptions } from "@/lib/auth"
import { getCurrentUser } from "@/lib/session"
import { Storage } from "@google-cloud/storage"

const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const storage = new Storage({
  projectId: process.env.GOOGLE_PROJECT_ID,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

const getNewsConfig = async (clientId: User["clientId"], id: string) => {
  const newsSettings = await db.newsSettings.findFirst({
    where: {
      id: Number(id),
      client_id: clientId,
    },
    select: {
      search_settings: true,
      name: true,
    },
  })

  if (newsSettings?.search_settings) {
    return {
      search_settings: JSON.parse(newsSettings.search_settings),
      name: newsSettings.name,
    }
  }

  return null
}

const getNewsFile = async (clientId: User["clientId"], id: string, name) => {
  try {
    const bucketName = "datachimp-news"
    const fileName = `${clientId}/${id}/${name
      .toLowerCase()
      .replace(" ", "_")}.csv`

    const bucket = storage.bucket(bucketName)
    const file = bucket.file(fileName)

    return new Promise((resolve) => {
      let fileContent = ""

      const stream = file.createReadStream()
      stream.on("data", (chunk) => {
        fileContent += chunk
      })

      stream.on("end", () => {
        resolve(fileContent)
      })

      stream.on("error", (err) => {
        console.error(`File '${fileName}' doesn't exist.`)
        resolve(null)
      })
    })
  } catch (err) {
    console.log(err)
    return null
  }
}

export default async function NewsIdSettingsPage({
  params,
}: {
  params: { id: string }
}) {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const newsConfig = await getNewsConfig(user.clientId, params.id)
  const newsFile = await getNewsFile(user.clientId, params.id, newsConfig?.name)

  return (
    <DashboardShell>
      <div className="md:max-w-4xl">
        <div className="mb-32">
          <NewsForm
            searchId={params.id}
            newsConfig={newsConfig?.search_settings}
            newsFileContent={newsFile}
          />
        </div>
      </div>
    </DashboardShell>
  )
}
