import { redirect } from "next/navigation"

import Link from "next/link"
import { db } from "@/lib/db"
import { User } from "@prisma/client"
import { dashboardConfig } from "@/config/dashboard"
import { Workflow } from "@/components/dashboard/workflow/workflow"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"
import { SecretManagerServiceClient } from "@google-cloud/secret-manager"

export const revalidate = 60

const projectId = process.env.GOOGLE_PROJECT_ID
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const secretClient = new SecretManagerServiceClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

const apiKeys = [
  {
    title: "Prospeo",
    description: "enrichment",
    logo: "prospeo",
    connection: "prospeo",
  },
  {
    title: "Apollo",
    description: "enrichment",
    logo: "apollo",
    connection: "apollo",
  },
  {
    title: "Lusha",
    description: "enrichment",
    logo: "lusha",
    connection: "lusha",
  },
]

const getWorkflowConfig = async (clientId: User["clientId"]) => {
  return await db.newsSettings.findFirst({
    where: {
      client_id: clientId,
    },
    select: {
      workflow_settings: true,
      workflow_status: true,
    },
  })
}

async function getConnections(clientId) {
  try {
    const url = "https://api.nango.dev/connection"
    const connections = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${process.env.NANGO_API_KEY}`,
      },
    })
    const result = await connections.json()
    const userConnections = result.connections.filter(
      (el) => el.connection_id === clientId
    )
    const secrets = await getSecrets(clientId)

    if (!secrets?.keys.length) {
      return userConnections
    }

    return userConnections.concat(secrets)
  } catch (error) {
    console.log(error)
    return null
  }
}

async function getSecrets(clientId) {
  let secretValues: string[] = []

  for (const apiKey of apiKeys) {
    const secretId = `${apiKey.connection}-${clientId}-secret`
    const name = `projects/${projectId}/secrets/${secretId}/versions/latest`

    try {
      const [version] = await secretClient.accessSecretVersion({ name })
      secretValues.push(apiKey.connection)
    } catch (err) {
      console.error(`Error retrieving secret ${secretId}, it might not exist.`)
    }
  }

  const provider = { provider: "enrichment", keys: secretValues }

  return provider
}

export default async function NewsWorkflowPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const workflowConfig: any = await getWorkflowConfig(user.clientId)
  let workflowSettings = null

  if (workflowConfig?.workflow_settings !== "") {
    workflowSettings = JSON.parse(workflowConfig?.workflow_settings)
  }

  const workflowStatus = workflowConfig?.workflow_status || ""
  const connections = await getConnections(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/news"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("new-hires")
        )}
      />
      {/* {connections?.length ? (
        <Workflow
          path="news"
          clientId={user.clientId}
          connections={connections}
          workflowStatus={workflowStatus}
          workflowSettings={workflowSettings}
        />
      ) : (
        <div className="flex h-64 items-center justify-center rounded-md border border-dashed bg-slate-50 text-neutral-500">
          <p>
            You need at least{" "}
            <Link href="/dashboard/settings/integrations">
              <span className="text-black">1 integration</span>
            </Link>{" "}
            activated to create a workflow 😉
          </p>
        </div>
      )} */}
      <div className="flex h-64 items-center justify-center rounded-md border border-dashed bg-slate-50 text-neutral-500">
        <p>In construction 😉</p>
      </div>
    </DashboardShell>
  )
}
