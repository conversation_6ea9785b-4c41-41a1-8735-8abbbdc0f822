import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { dashboardConfig } from "@/config/dashboard"
import ItemSkeleton from "@/components/dashboard/item-skeleton"

export default function NewHiresResultsLoading() {
  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/new-hires"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("new-hires")
        )}
      />
      <div>
        <ItemSkeleton />
        <ItemSkeleton />
        <ItemSkeleton />
        <ItemSkeleton />
        <ItemSkeleton />
      </div>
    </DashboardShell>
  )
}
