import { redirect } from "next/navigation"

import { db } from "@/lib/db"
import { User } from "@prisma/client"
import { dashboardConfig } from "@/config/dashboard"
import { Workflow } from "@/components/dashboard/workflow/workflow"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"

export const revalidate = 60

const getWorkflowConfig = async (clientId: User["clientId"]) => {
  return await db.jobOffersSettings.findFirst({
    where: {
      client_id: clientId,
    },
    select: {
      workflow_settings: true,
      workflow_status: true,
    },
  })
}

async function getConnections(clientId) {
  try {
    const url = "https://api.nango.dev/connection"
    const connections = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${process.env.NANGO_API_KEY}`,
      },
    })
    const result = await connections.json()
    const userConnections = result.connections.filter(
      (el) => el.connection_id === clientId
    )

    return userConnections
  } catch (error) {
    console.log(error)
    return null
  }
}

export default async function NewHiredWorkflowPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const workflowConfig: any = await getWorkflowConfig(user.clientId)
  let workflowSettings = null

  if (workflowConfig?.workflow_settings !== "") {
    workflowSettings = JSON.parse(workflowConfig?.workflow_settings)
  }

  const workflowStatus = workflowConfig?.workflow_status || ""
  const connections = await getConnections(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/job-offers"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("job-offers")
        )}
      />
      <Workflow
        path="job-offers"
        clientId={user.clientId}
        connections={connections}
        workflowStatus={workflowStatus}
        workflowSettings={workflowSettings}
      />
    </DashboardShell>
  )
}
