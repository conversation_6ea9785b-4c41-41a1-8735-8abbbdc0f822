import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import SearchTable from "@/components/dashboard/search-table"

import { db } from "@/lib/db"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"

async function getSearchesIds(clientId: User["clientId"]) {
  const titlesSettings = await db.jobOffersSettings.findMany({
    where: {
      client_id: clientId,
    },
    select: {
      id: true,
      name: true,
    },
  })

  if (titlesSettings?.length) {
    return titlesSettings
  }

  return []
}

export default async function JobOffersSearchPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const searchesIds = await getSearchesIds(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/job-offers"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("job-offers")
        )}
      />
      <SearchTable searchesIds={searchesIds} path="job-offers" />
    </DashboardShell>
  )
}
