import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { SearchSettings } from "@/components/dashboard/job-offers/search-settings"

import { db } from "@/lib/db"
import { authOptions } from "@/lib/auth"
import { getCurrentUser } from "@/lib/session"

const getJobOffersConfig = async (clientId: User["clientId"], id: string) => {
  const jobOffersSettings = await db.jobOffersSettings.findFirst({
    where: {
      id: Number(id),
      client_id: clientId,
    },
    select: {
      search_settings: true,
      name: true,
    },
  })

  if (jobOffersSettings?.search_settings) {
    return {
      search_settings: JSON.parse(jobOffersSettings.search_settings),
      name: jobOffersSettings.name,
    }
  }

  return null
}

export default async function JobOffersSearchIdPage({
  params,
}: {
  params: { id: string }
}) {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const jobOffersConfig = await getJobOffersConfig(user.clientId, params.id)

  return (
    <DashboardShell>
      <div className="md:max-w-4xl">
        <div className="mb-32">
          <SearchSettings
            searchId={params.id}
            clientId={user.clientId}
            jobOffersConfig={jobOffersConfig?.search_settings}
            nameConfig={jobOffersConfig?.name}
          />
        </div>
      </div>
    </DashboardShell>
  )
}
