import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import { TitlesForm } from "@/components/dashboard/new-hires/titles-form"

import { db } from "@/lib/db"
import { authOptions } from "@/lib/auth"
import { getCurrentUser } from "@/lib/session"

const getTitlesConfig = async (clientId: User["clientId"], id: string) => {
  const titlesSettings = await db.newPositionSettings.findFirst({
    where: {
      id: Number(id),
      client_id: clientId,
    },
    select: {
      search_settings: true,
      name: true,
    },
  })

  if (titlesSettings?.search_settings) {
    return {
      search_settings: JSON.parse(titlesSettings.search_settings),
      name: titlesSettings.name,
    }
  }

  return null
}

export default async function NewHiredSettingsPage({
  params,
}: {
  params: { id: string }
}) {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const titlesConfig = await getTitlesConfig(user.clientId, params.id)

  return (
    <DashboardShell>
      <div className="md:max-w-4xl">
        <div className="mb-32">
          <TitlesForm
            searchId={params.id}
            titlesConfig={titlesConfig?.search_settings}
            nameConfig={titlesConfig?.name}
          />
        </div>
      </div>
    </DashboardShell>
  )
}
