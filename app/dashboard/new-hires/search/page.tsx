import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import SearchTable from "@/components/dashboard/search-table"

import { db } from "@/lib/db"
import { authOptions } from "@/lib/auth"
import { getCurrentUser } from "@/lib/session"

async function getSearchesIds(clientId: User["clientId"]) {
  const titlesSettings = await db.newPositionSettings.findMany({
    where: {
      client_id: clientId,
    },
    select: {
      id: true,
      name: true,
      search: true,
    },
  })

  if (titlesSettings?.length) {
    return titlesSettings
  }

  return []
}

export default async function NewHiredSettingsPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const searchesIds = await getSearchesIds(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/new-hires"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("new-hires")
        )}
      />
      <SearchTable searchesIds={searchesIds} path="new-hires" />
    </DashboardShell>
  )
}
