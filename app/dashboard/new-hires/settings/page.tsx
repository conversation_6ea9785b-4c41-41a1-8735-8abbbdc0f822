import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { WebhookItem } from "@/components/dashboard/webhook-item"
import { WebhookForm } from "@/components/dashboard/webhook-form"
import { SchedulerForm } from "@/components/dashboard/scheduler-form"
import { SchedulerItem } from "@/components/dashboard/scheduler-item"
import { EmailNotificationForm } from "@/components/dashboard/email-notification-form"

import { db } from "@/lib/db"
import { CloudSchedulerClient } from "@google-cloud/scheduler"
import { google } from "@google-cloud/scheduler/build/protos/protos"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"

export const revalidate = 60

interface SchedulerConfig {
  schedule: string
  state:
    | google.cloud.scheduler.v1.Job.State
    | "STATE_UNSPECIFIED"
    | "ENABLED"
    | "PAUSED"
    | "DISABLED"
    | "UPDATE_FAILED"
}

const getSchedulerConfig = async (clientId: User["clientId"]) => {
  if (!process.env.GOOGLE_SERVICE_KEY) {
    throw new Error(
      "GOOGLE_SERVICE_KEY is not defined in the environment variables"
    )
  }

  const credential = JSON.parse(
    Buffer.from(process.env.GOOGLE_SERVICE_KEY, "base64").toString()
  )
  const projectId = process.env.GOOGLE_PROJECT_ID

  const client = new CloudSchedulerClient({
    projectId: projectId,
    credentials: {
      client_email: credential.client_email,
      private_key: credential.private_key,
    },
  })

  const request = {
    name: `projects/${projectId}/locations/europe-west1/jobs/new-hires-${clientId}`,
  }
  try {
    const resultArr = await client.getJob(request)
    const job: any = resultArr.find((job) => job !== null)

    if (job) {
      return {
        schedule: job.schedule,
        state: job.state,
        timezone: job.timeZone,
      }
    } else {
      return null
    }
  } catch (error) {
    console.log("Error while retrieving the scheduler. It might not exist!")
    return null
  }
}

const getNotifsConfig = async (
  clientId: User["clientId"],
  userId: User["id"]
) => {
  return await db.newPositionSettings.findFirst({
    where: {
      client_id: clientId,
      client: {
        users: {
          some: {
            id: userId,
          },
        },
      },
    },
    select: {
      webhook_url: true,
      webhook_status: true,
      client: {
        select: {
          users: {
            where: {
              id: userId,
            },
            select: {
              newHiresEmail: true,
            },
          },
        },
      },
    },
  })
}

export default async function NewHiredSettingsPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const schedulerConfig = await getSchedulerConfig(user.clientId)
  const notifsConfig = await getNotifsConfig(user.clientId, user.id)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/new-hires"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("new-hires")
        )}
      />
      <div className="md:max-w-4xl">
        <div className="mb-16">
          <h2 className="text-md mb-1 font-semibold">Scheduler</h2>
          <p className="mb-6 text-sm text-neutral-500">
            At which frequency do you want to retrieve your data?
          </p>
          {schedulerConfig ? (
            <div className="divide-y divide-neutral-200 rounded-md border border-slate-200">
              <SchedulerItem
                scheduler={{
                  schedule: schedulerConfig?.schedule,
                  state: schedulerConfig?.state as string,
                  timezone: schedulerConfig?.timezone as string,
                  path: "new-hires",
                }}
              />
            </div>
          ) : (
            <SchedulerForm path="new-hires" />
          )}
        </div>
        {user.clientId !== "clxizm16y0000l40c0k4kjivg" &&
        user.clientId !== "clmeu2ril0000jv0fqp6own4e" &&
        user.clientId !== "cm1kja51x0000mn0cf3db8j28" &&
        user.clientId !== "cloint5sj0000l60fwbzj1p52" ? (
          <div className="mb-16">
            <h2 className="text-md mb-1 font-semibold">Webhook</h2>
            <p className="mb-6 text-sm text-neutral-500">
              A webhook allows you to receive HTTP requests containing your
              data.
            </p>
            {notifsConfig?.webhook_url ? (
              <div className="divide-y divide-neutral-200 rounded-md border border-slate-200">
                <WebhookItem
                  webhook={{
                    url: notifsConfig.webhook_url,
                    status: notifsConfig.webhook_status,
                    path: "new-hires",
                  }}
                />
              </div>
            ) : (
              <WebhookForm path="new-hires" />
            )}
          </div>
        ) : null}
        <div className="mb-64">
          <EmailNotificationForm
            email={{
              status: notifsConfig?.client.users[0].newHiresEmail,
              path: "new-hires",
            }}
          />
        </div>
      </div>
    </DashboardShell>
  )
}
