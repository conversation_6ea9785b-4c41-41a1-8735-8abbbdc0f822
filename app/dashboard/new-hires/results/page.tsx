import { redirect } from "next/navigation"

import { Storage } from "@google-cloud/storage"
import { getCurrentUser } from "@/lib/session"
import { User } from "@prisma/client"
import { authOptions } from "@/lib/auth"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardHeader } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"
import PostItem from "@/components/dashboard/post-item"
import { EmptyPlaceholder } from "@/components/dashboard/empty-placeholder"

export const revalidate = 60

interface Result {
  id: string
  createdAt: Date
  items: any
  link: any
}

const getFilesForUser = async (clientId: User["clientId"]) => {
  if (!process.env.GOOGLE_SERVICE_KEY) {
    throw new Error(
      "GOOGLE_SERVICE_KEY is not defined in the environment variables"
    )
  }

  const credential = JSON.parse(
    Buffer.from(process.env.GOOGLE_SERVICE_KEY, "base64").toString()
  )

  const storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: {
      client_email: credential.client_email,
      private_key: credential.private_key,
    },
  })
  const bucketName = "datachimp-insights"
  const options = {
    prefix: `new-hires/${clientId}/`,
    delimiter: "/",
  }

  const [files] = await storage.bucket(bucketName).getFiles(options)

  const results: Result[] = []
  for (let file of files) {
    if (!file.name.includes(".csv")) continue

    const metaDataPromise = await file.getMetadata()
    const metaData = metaDataPromise[0]
    const link = metaData.mediaLink

    results.push({
      id: file.name,
      createdAt: metaData.timeCreated,
      items: metaData.metadata?.row_count,
      link: link,
    })
  }

  const sortedResults = results.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  return sortedResults.slice(0, 10)
}

export default async function NewHiredResultsPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const files = await getFilesForUser(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/new-hires"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("new-hires")
        )}
      />
      <div className="mb-32">
        {files?.length ? (
          <div className="divide-y divide-neutral-200 rounded-md border border-slate-200">
            {files.map((post) => (
              <PostItem key={post.id} post={post} insight="new-hires" />
            ))}
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="newHired" />
            <EmptyPlaceholder.Title>No insights</EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              You don&apos;t have any new hire insight yet.
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
