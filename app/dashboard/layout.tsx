import { notFound } from "next/navigation"

import { dashboardConfig } from "@/config/dashboard"
import { getCurrentUser } from "@/lib/session"
import { DashboardNav } from "@/components/dashboard/nav"
import { UserAccountNav } from "@/components/dashboard/user-account-nav"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default async function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const user = await getCurrentUser()

  if (!user) {
    return notFound()
  }

  return (
    <div className="mx-auto flex flex-col space-y-6">
      <div className="grid gap-12 md:grid-cols-[200px_1fr]">
        <aside
          className="container hidden w-[250px] flex-col bg-slate-100 md:flex"
          style={{
            height: "100vh",
            position: "sticky",
            top: "0px",
          }}
        >
          <DashboardNav items={dashboardConfig.sidebarNav} />
        </aside>
        <div className="container">
          <header className="sticky top-0 z-40 bg-white">
            <div className="flex h-16 items-center justify-end py-4">
              <UserAccountNav
                user={{
                  name: user.name,
                  image: user.image,
                  email: user.email,
                }}
              />
            </div>
          </header>
          <main className="container mt-6 flex w-full flex-1 flex-col">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
