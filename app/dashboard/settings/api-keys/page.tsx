import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { getCurrentUser } from "@/lib/session"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import ApiKeyCard from "@/components/dashboard/settings/api-keys-card"

import { SecretManagerServiceClient } from "@google-cloud/secret-manager"

interface SecretValue {
  [key: string]: string | undefined
}

const projectId = process.env.GOOGLE_PROJECT_ID
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const secretClient = new SecretManagerServiceClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

const apiKeys = [
  {
    title: "Prospeo",
    description: "enrichment",
    logo: "prospeo",
    connection: "prospeo",
  },
  {
    title: "Apollo",
    description: "enrichment",
    logo: "apollo",
    connection: "apollo",
  },
  {
    title: "Lusha",
    description: "enrichment",
    logo: "lusha",
    connection: "lusha",
  },
  {
    title: "Dropcontact",
    description: "enrichment",
    logo: "dropcontact",
    connection: "dropcontact",
  },
]

async function getSecrets(user) {
  let secretValues: SecretValue[] = []

  for (const apiKey of apiKeys) {
    const secretId = `${apiKey.connection}-${user.clientId}-secret`
    const name = `projects/${projectId}/secrets/${secretId}/versions/latest`

    try {
      const [version] = await secretClient.accessSecretVersion({ name })
      const secretData = version?.payload?.data?.toString()
      secretValues.push({ [apiKey.connection]: secretData })
    } catch (err) {
      console.error(`Error retrieving secret ${secretId}, it might not exist.`)
      secretValues.push({ [apiKey.connection]: undefined })
    }
  }

  return secretValues
}

export default async function ApiKeysPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const secrets = await getSecrets(user)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/settings"
        items={dashboardConfig.dashboardHeader.settings}
      />
      <div className="grid grid-cols-2 gap-x-3 gap-y-6">
        {apiKeys.map((apiKey, i) => (
          <ApiKeyCard key={i} integration={apiKey} secrets={secrets} />
        ))}
      </div>
    </DashboardShell>
  )
}
