import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { dashboardConfig } from "@/config/dashboard"
import ItemSkeleton from "@/components/dashboard/item-skeleton"

export default function ApiKeysLoading() {
  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/settings"
        items={dashboardConfig.dashboardHeader.settings}
      />
      <div>
        <ItemSkeleton />
        <ItemSkeleton />
        <ItemSkeleton />
        <ItemSkeleton />
        <ItemSkeleton />
      </div>
    </DashboardShell>
  )
}
