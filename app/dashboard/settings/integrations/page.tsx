import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { getCurrentUser } from "@/lib/session"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import IntegrationCard from "@/components/dashboard/settings/integration-card"

const integrations = [
  {
    title: "Hubspot",
    description: "CRM",
    logo: "hubspot",
    connection: "hubspot",
  },
  {
    title: "Salesforce",
    description: "CRM",
    logo: "salesforce",
    connection: "salesforce",
  },
  {
    title: "Pipedrive",
    description: "CRM",
    logo: "pipedrive",
    connection: "pipedrive",
  },
  {
    title: "Slack",
    description: "communications",
    logo: "slack",
    connection: "slack",
  },
  {
    title: "Teams",
    description: "communications",
    logo: "teams",
    connection: "microsoft-teams",
  },
]

async function getConnections(user) {
  const url = "https://api.nango.dev/connection"
  const connections = await fetch(url, {
    method: "GET",
    headers: {
      Authorization: `Bear<PERSON> ${process.env.NANGO_API_KEY}`,
    },
  })
  const result = await connections.json()
  const userConnections = result.connections.filter(
    (el) => el.connection_id === user.clientId
  )

  return userConnections
}

export default async function SettingsPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const connections = await getConnections(user)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/settings"
        items={dashboardConfig.dashboardHeader.settings}
      />
      <div className="grid grid-cols-3 gap-3 md:max-w-4xl">
        {integrations.map((integration, i) => (
          <IntegrationCard
            key={i}
            connected={connections?.some(
              (connection) => connection.provider === integration.connection
            )}
            integration={integration}
            user={user}
          />
        ))}
      </div>
    </DashboardShell>
  )
}
