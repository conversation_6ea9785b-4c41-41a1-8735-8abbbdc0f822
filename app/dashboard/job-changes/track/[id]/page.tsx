import { User } from "@prisma/client"
import { redirect } from "next/navigation"
import { DashboardShell } from "@/components/dashboard/shell"
import SearchSettings from "@/components/dashboard/job-changes/search-settings"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

const getJobChangesConfig = async (clientId: User["clientId"], id: string) => {
  const jobChangesSettings = await db.jobChangesSettings.findFirst({
    where: {
      id: Number(id),
      client_id: clientId,
    },
    select: {
      search_settings: true,
      name: true,
    },
  })

  if (jobChangesSettings?.search_settings) {
    return {
      search_settings: JSON.parse(jobChangesSettings.search_settings),
      name: jobChangesSettings.name,
    }
  }

  return null
}

export default async function JobChangesIdSettingsPage({
  params,
}: {
  params: { id: string }
}) {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const jobChangesConfig = await getJobChangesConfig(user.clientId, params.id)

  return (
    <DashboardShell>
      <div className="md:max-w-4xl">
        <div className="mb-32">
          <SearchSettings
            searchId={params.id}
            jobChangesConfig={jobChangesConfig}
          />
        </div>
      </div>
    </DashboardShell>
  )
}
