import { redirect } from "next/navigation"
import { db } from "@/lib/db"
import { User } from "@prisma/client"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardShell } from "@/components/dashboard/shell"
import { DashboardHeader } from "@/components/dashboard/header"
import { getCurrentUser } from "@/lib/session"
import { authOptions } from "@/lib/auth"

import SearchTable from "@/components/dashboard/search-table"

async function getSearchesIds(clientId: User["clientId"]) {
  const titlesSettings = await db.jobChangesSettings.findMany({
    where: {
      client_id: clientId,
    },
    select: {
      id: true,
      name: true,
    },
  })

  if (titlesSettings?.length) {
    return titlesSettings
  }

  return []
}

export default async function JobChangesSearchPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const searchesIds = await getSearchesIds(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/job-changes"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("job-changes")
        )}
      />
      <SearchTable searchesIds={searchesIds} path="job-changes" />
    </DashboardShell>
  )
}
