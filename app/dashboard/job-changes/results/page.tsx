import { redirect } from "next/navigation"

import { Storage } from "@google-cloud/storage"
import { getCurrentUser } from "@/lib/session"
import { User } from "@prisma/client"
import { authOptions } from "@/lib/auth"
import { dashboardConfig } from "@/config/dashboard"
import { DashboardHeader } from "@/components/dashboard/header"
import { DashboardShell } from "@/components/dashboard/shell"
import PostItem from "@/components/dashboard/post-item"
import { EmptyPlaceholder } from "@/components/dashboard/empty-placeholder"
import {
  getGoogleCredentials,
  getGoogleProjectId,
} from "@/lib/google-credentials"

export const revalidate = 60

interface Result {
  id: string
  createdAt: Date
  items: any
  link: any
}

const getFilesForUser = async (clientId: User["clientId"]) => {
  const credentials = getGoogleCredentials()

  if (!credentials) {
    console.log("Google credentials not available")
    return []
  }

  const storage = new Storage({
    projectId: getGoogleProjectId(),
    credentials: {
      client_email: credentials.client_email,
      private_key: credentials.private_key,
    },
  })
  const bucketName = "datachimp-insights"
  const options = {
    prefix: `job-changes/${clientId}/`,
    delimiter: "/",
  }

  const [files] = await storage.bucket(bucketName).getFiles(options)

  const results: Result[] = []
  for (let file of files) {
    if (!file.name.includes(".csv")) continue

    const metaDataPromise = await file.getMetadata()
    const metaData = metaDataPromise[0]
    const link = metaData.mediaLink

    results.push({
      id: file.name,
      createdAt: metaData.timeCreated,
      items: metaData.metadata?.row_count,
      link: link,
    })
  }

  const sortedResults = results.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  return sortedResults.slice(0, 10)
}

export default async function NewHiredResultsPage() {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  const files = await getFilesForUser(user.clientId)

  return (
    <DashboardShell>
      <DashboardHeader
        path="/dashboard/job-changes"
        items={dashboardConfig.dashboardHeader.insights.filter(
          (insight) =>
            insight.default || insight.insights?.includes("job-changes")
        )}
      />
      <div>
        {files?.length ? (
          <div className="divide-y divide-neutral-200 rounded-md border border-slate-200">
            {files.map((post) => (
              <PostItem key={post.id} post={post} insight="job-changes" />
            ))}
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="userChange" />
            <EmptyPlaceholder.Title>No insights</EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              You don&apos;t have any job change insight yet.
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
