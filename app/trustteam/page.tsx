import { redirect } from "next/navigation"
import Image from "next/image"

import { Storage } from "@google-cloud/storage"
import { getCurrentUser } from "@/lib/session"
import Papa from "papaparse"
import { authOptions } from "@/lib/auth"
import {
  getGoogleCredentials,
  getGoogleProjectId,
} from "@/lib/google-credentials"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Wrench, Star, Lightbulb, BookOpen } from "lucide-react"
import { Button } from "@/components/ui/button"

interface JobOffer {
  company_name: string
  title: string
  domain: string
  company_url: string
  summary: string
  analysis: string
  job_url: string
  relevancy: number
  tools: string[]
}

const getFilesForUser = async (
  batchId: string
): Promise<{ jobOffers: JobOffer[]; uploadDate: string }> => {
  const credentials = getGoogleCredentials()

  if (!credentials) {
    console.log("Google credentials not available")
    return { jobOffers: [], uploadDate: "" }
  }

  const storage = new Storage({
    projectId: getGoogleProjectId(),
    credentials: {
      client_email: credentials.client_email,
      private_key: credentials.private_key,
    },
  })

  const bucketName = "datachimp-csv-batches"
  const filePath = `${batchId}.csv`

  try {
    const bucket = storage.bucket(bucketName)
    const file = bucket.file(filePath)

    // Get file metadata to extract upload date
    const [metadata] = await file.getMetadata()
    const uploadDate = metadata.timeCreated || new Date().toISOString()

    // Get the file content
    const [content] = await file.download()
    const csvContent = content.toString("utf-8")

    // Parse CSV content using Papaparse
    return new Promise((resolve, reject) => {
      Papa.parse(csvContent, {
        header: true,
        complete: (results) => {
          const jobOffers = results.data.map((row: any) => {
            let tools = []

            // Safely parse matched_technologies
            try {
              if (
                row.matched_technologies &&
                row.matched_technologies !== "undefined" &&
                row.matched_technologies.trim() !== ""
              ) {
                tools = JSON.parse(row.matched_technologies)
              }
            } catch (error) {
              console.error(
                `Failed to parse tools for ${
                  row.company_name || "unknown"
                }: ${error}`
              )
            }

            return {
              company_name: row.company_name || "",
              title: row.title || "",
              domain: row.domain || "",
              company_url: row.company_url || "",
              summary: row.summary || "",
              analysis: row.analysis || "",
              job_url: row.job_url || "",
              relevancy: parseInt(row.relevancy) || 0,
              tools: Array.isArray(tools) ? tools : [],
            }
          })
          resolve({ jobOffers, uploadDate })
        },
        error: (error) => {
          reject(new Error(`Failed to parse CSV: ${error}`))
        },
      })
    })
  } catch (error) {
    console.error("Error fetching file:", error)
    return { jobOffers: [], uploadDate: new Date().toISOString() }
  }
}

export default async function JobOffersBatch({
  searchParams,
}: {
  searchParams: { batch_id: string }
}) {
  const user = await getCurrentUser()

  if (!user) {
    if (authOptions.pages && authOptions.pages.signIn) {
      redirect(authOptions.pages.signIn)
    } else {
      return
    }
  }

  if (!searchParams.batch_id) {
    redirect("/")
  }

  const { jobOffers, uploadDate } = await getFilesForUser(searchParams.batch_id)

  const formattedDate = uploadDate
    ? new Intl.DateTimeFormat("en-US", {
        month: "long",
        year: "numeric",
      }).format(new Date(uploadDate))
    : "Report"

  return (
    <div className="text-foreground min-h-screen bg-white">
      <header className="border-b bg-gradient-to-r from-pink-50 via-purple-50 to-emerald-50 p-6">
        <div className="container mx-auto flex items-center justify-between">
          <div className="flex items-center gap-8">
            <span className="font-cal text-xl">datachimp.</span>
            <span className="text-2xl font-bold">×</span>
            <div className="rounded bg-gray-900 px-3 py-2">
              <Image
                src="/images/trustteam.png"
                alt="Trustteam Logo"
                width={150}
                height={40}
                className="h-8 w-auto"
              />
            </div>
          </div>
          <p className="text-muted-foreground text-sm">{formattedDate}</p>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="mb-3 text-3xl font-bold">
            Luxembourg Job Postings Report
          </h1>
          <p className="text-muted-foreground">
            Insights from job postings related to IT infrastructure and security
          </p>
        </div>

        <div className="grid gap-6">
          {jobOffers.map((job, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl font-bold">
                    {job.company_name} - {job.title}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-2">
                    <BookOpen className="text-muted-foreground mt-1 h-4 w-4 shrink-0" />
                    <p className="text-sm">
                      <span className="font-semibold">Summary:</span>{" "}
                      {job.summary}
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <Lightbulb className="text-muted-foreground mt-1 h-4 w-4 shrink-0" />
                    <p className="text-sm">
                      <span className="font-semibold">Analysis:</span>{" "}
                      {job.analysis}
                    </p>
                  </div>
                  <div className="flex items-start gap-2">
                    <Wrench className="text-muted-foreground mt-1 h-4 w-4 shrink-0" />
                    <div className="flex gap-1 text-sm">
                      <span className="font-semibold">Tools:</span>{" "}
                      <div className="space-x-2">
                        {job.tools.map((tool, i) => (
                          <span className="underline" key={i}>
                            {tool.replace("_", " ")}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < job.relevancy
                                ? "fill-yellow-500 text-yellow-500"
                                : "text-muted-foreground"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-muted-foreground text-sm">
                        Relevancy Score
                      </span>
                    </div>
                    <Button asChild>
                      <a
                        href={job.job_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        View Source Job Offer
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  )
}
