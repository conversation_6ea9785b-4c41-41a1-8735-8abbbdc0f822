{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/ui/*": ["components/ui/*"], "@/hooks/*": ["hooks/*"], "@/lib/*": ["lib/*"], "@/config/*": ["config/*"], "@/styles/*": ["styles/*"], "@/prisma/*": ["prisma/*"]}, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "content/pages/about.mdx"], "exclude": ["node_modules"]}