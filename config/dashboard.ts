import { DashboardConfig } from "types"

export const dashboardConfig: DashboardConfig = {
  sidebarNav: [
    {
      title: "New hires",
      href: "/dashboard/new-hires/results",
      icon: "newHired",
      dropdown: true,
    },
    {
      title: "Job changes",
      href: "/dashboard/job-changes/results",
      icon: "userChange",
      dropdown: true,
    },
    {
      title: "Job posts",
      href: "/dashboard/job-offers/results",
      icon: "jobOffers",
      dropdown: false,
    },
    {
      title: "Settings",
      href: "/dashboard/settings/integrations",
      icon: "settings",
      dropdown: false,
    },
  ],
  dashboardHeader: {
    insights: [
      {
        title: "Results",
        path: "results",
        default: true,
      },
      {
        title: "Search",
        path: "search",
        default: false,
        insights: ["new-hires", "job-offers"],
      },
      {
        title: "Track",
        path: "track",
        default: false,
        insights: ["job-changes"],
      },
      {
        title: "Settings",
        path: "settings",
        default: false,
        insights: ["new-hires", "job-offers", "job-changes"],
      },
      {
        title: "Workflow",
        path: "workflow",
        default: false,
        insights: ["new-hires", "job-offers", "job-changes"],
      },
    ],
    settings: [
      {
        title: "Integrations",
        path: "integrations",
        default: true,
      },
      {
        title: "API Keys",
        path: "api-keys",
        default: false,
      },
    ],
  },
}
