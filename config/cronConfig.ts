import { CronTabsConfig, CronOptionsConfig } from "types"

export const cronTabs: CronTabsConfig = [
  {
    value: "daily",
    content: "Daily",
  },
  {
    value: "weekly",
    content: "Weekly",
  },
  {
    value: "monthly",
    content: "Monthly",
  },
]

export const cronOptions: any = {
  monthDays: [
    { value: "1", content: "1" },
    { value: "2", content: "2" },
    { value: "3", content: "3" },
    { value: "4", content: "4" },
    { value: "5", content: "5" },
    { value: "6", content: "6" },
    { value: "7", content: "7" },
    { value: "8", content: "8" },
    { value: "9", content: "9" },
    { value: "10", content: "10" },
    { value: "11", content: "11" },
    { value: "12", content: "12" },
    { value: "13", content: "13" },
    { value: "14", content: "14" },
    { value: "15", content: "15" },
    { value: "16", content: "16" },
    { value: "17", content: "17" },
    { value: "18", content: "18" },
    { value: "19", content: "19" },
    { value: "20", content: "20" },
    { value: "21", content: "21" },
    { value: "22", content: "22" },
    { value: "23", content: "23" },
    { value: "24", content: "24" },
    { value: "25", content: "25" },
    { value: "26", content: "26" },
    { value: "27", content: "27" },
    { value: "28", content: "28" },
    { value: "29", content: "29" },
    { value: "30", content: "30" },
    { value: "31", content: "31" },
  ],
  weekDays: [
    { value: "1", content: "Monday" },
    { value: "2", content: "Tuesday" },
    { value: "3", content: "Wednesday" },
    { value: "4", content: "Thursday" },
    { value: "5", content: "Friday" },
    { value: "6", content: "Saturday" },
    { value: "0", content: "Sunday" },
  ],
  hours: [
    { value: "1", content: "01" },
    { value: "2", content: "02" },
    { value: "3", content: "03" },
    { value: "4", content: "04" },
    { value: "5", content: "05" },
    { value: "6", content: "06" },
    { value: "7", content: "07" },
    { value: "8", content: "08" },
    { value: "9", content: "09" },
    { value: "10", content: "10" },
    { value: "11", content: "11" },
    { value: "12", content: "12" },
  ],
  minutes: [
    { value: "0", content: "00" },
    { value: "15", content: "15" },
    { value: "30", content: "30" },
    { value: "45", content: "45" },
  ],
  timesOfDay: [
    { value: "AM", content: "AM" },
    { value: "PM", content: "PM" },
  ],
}
