"use client"

import * as z from "zod"
import useS<PERSON> from "swr"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { hubspotSchema } from "@/lib/validations/hubspot"

import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Separator } from "@/components/ui/separator"
import ObjectsChoice from "./hubspot/objects-choice"
import ObjectCheck from "./hubspot/objects-check"
import CompanyCreationChoice from "./hubspot/company-creation-choice"
import ContactCreationChoice from "./hubspot/contact-creation-choice"
import MatchingKey from "@/components/dashboard/hubspot/matching-key"
import ObjectsFields from "@/components/dashboard/hubspot/objects-fields"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { Form } from "@/components/react-hook-form/form"
import { fetcher } from "@/lib/utils"

interface EnrichmentProviders {
  bettercontact?: boolean
  prospeo?: boolean
  apollo?: boolean
  lusha?: boolean
  dropcontact?: boolean
}

const hasTrueValue = (obj) => {
  if (obj) {
    return Object.values(obj).some((value) => value === true)
  }
}

const isCustomObjectValid = (formValues) =>
  formValues.hubspot_object_choice === "custom" &&
  formValues.hubspot_custom_properties_mapping.length &&
  formValues.hubspot_custom_properties_mapping[0]?.datachimp?.value &&
  formValues.hubspot_custom_properties_mapping[0]?.hubspot?.value

const isStandardObjectValid = (formValues) =>
  formValues.hubspot_object_choice === "standard" &&
  formValues.datachimp_hubspot_matching_key?.datachimp?.value &&
  formValues.datachimp_hubspot_matching_key?.hubspot?.value

const isCompanyCreationValid = (formValues) =>
  formValues.hubspot_company_creation === "yes" &&
  formValues.hubspot_company_properties_mapping.length &&
  formValues.hubspot_company_properties_mapping[0]?.datachimp?.value &&
  formValues.hubspot_company_properties_mapping[0]?.hubspot?.value

const isCompanyNotCreationValid = (formValues) =>
  formValues.hubspot_company_creation === "no"

const isContactCreationValid = (formValues) =>
  formValues.hubspot_contact_creation === "yes" &&
  formValues.hubspot_contact_properties_mapping.length &&
  formValues.hubspot_contact_properties_mapping[0]?.datachimp?.value &&
  formValues.hubspot_contact_properties_mapping[0]?.hubspot?.value

const isContactNotCreationValid = (formValues) =>
  formValues.hubspot_contact_creation === "no"

const getCardDescription = (path) => {
  switch (path) {
    case "new-hires":
      return "new hires"
    case "job-changes":
      return "job changes"
    case "job-offers":
      return "job offers"
  }
}

export default function HubspotCard({
  path,
  step,
  setSteps,
  stepNumber,
  clientId,
}) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)
  const [objectSynced, setObjectSynced] = useState(false)
  const [enrichmentProviders, setEnrichmentProviders] =
    useState<EnrichmentProviders>()
  const [customObjectProperties, setCustomObjectProperties] = useState()

  async function onSubmit(data: z.infer<typeof hubspotSchema>) {
    setIsSaving(true)

    const response = await fetch(`/api/${path}/workflow?key=hubspot`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    setSteps((steps) =>
      steps.map((step) => {
        if (step.type === "hubspot") {
          return { ...step, open: false }
        } else {
          return step
        }
      })
    )

    router.refresh()

    return toast({
      title: "Your Hubspot settings have been saved 🎉",
      description: "You can configure the rest of your workflow.",
    })
  }

  const form = useForm<z.infer<typeof hubspotSchema>>({
    resolver: zodResolver(hubspotSchema),
    defaultValues: {
      hubspot_object_choice: "",
      hubspot_object_name: "",
      datachimp_hubspot_matching_key: {
        datachimp: { label: "", value: "" },
        hubspot: { label: "", value: "" },
      },
      hubspot_company_creation: "",
      hubspot_contact_creation: "",
      hubspot_custom_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          hubspot: { label: "", value: "", type: "standard" },
        },
      ],
      hubspot_company_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          hubspot: { label: "", value: "", type: "standard" },
        },
      ],
      hubspot_contact_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          hubspot: { label: "", value: "", type: "standard" },
        },
      ],
    },
  })

  const { data, isLoading } = useSWR(
    `/api/${path}/workflow?key=hubspot`,
    fetcher
  )

  const { data: enrichment } = useSWR(
    `/api/${path}/workflow?key=enrichment`,
    fetcher
  )

  const { data: companyProperties } = useSWR(
    `/api/hubspot/properties?object=companies`,
    fetcher
  )

  const { data: contactProperties } = useSWR(
    `/api/hubspot/properties?object=contacts`,
    fetcher
  )

  const { watch } = form
  const formValues = watch()

  const shouldDisplayCompanyCreation =
    (isCustomObjectValid(formValues) && objectSynced) ||
    isStandardObjectValid(formValues)

  const shouldDisplayContactCreation =
    ((isCustomObjectValid(formValues) && objectSynced) ||
      isStandardObjectValid(formValues)) &&
    (isCompanyCreationValid(formValues) ||
      isCompanyNotCreationValid(formValues)) &&
    hasTrueValue(enrichmentProviders)

  const shouldDisplaySaveButton = () => {
    if (
      ((isCustomObjectValid(formValues) && objectSynced) ||
        isStandardObjectValid(formValues)) &&
      (isCompanyCreationValid(formValues) ||
        isCompanyNotCreationValid(formValues)) &&
      !hasTrueValue(enrichmentProviders)
    ) {
      return true
    } else if (
      isContactCreationValid(formValues) ||
      isContactNotCreationValid(formValues)
    ) {
      return true
    }
  }

  // Reload data in the form every time the step is edited
  useEffect(() => {
    if (data?.hubspot_settings) {
      form.reset(JSON.parse(data.hubspot_settings))
    }
  }, [data, step])

  // Check which are the enrichment providers activated
  useEffect(() => {
    if (enrichment?.enrichment_settings) {
      const parsedProviders = JSON.parse(enrichment.enrichment_settings)
      setEnrichmentProviders(parsedProviders)
    } else if (
      clientId &&
      path === "job-offers" &&
      (clientId === "clye6efp50000jy0crv1tehwh" ||
        clientId === "cleq1dvj50000l1acysero2on")
    ) {
      setEnrichmentProviders({ bettercontact: true })
    }
  }, [enrichment, step])

  if (isLoading) {
    return (
      <CardItem
        path={path}
        isLoading
        setSteps={setSteps}
        stepNumber={stepNumber}
      />
    )
  }

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          path={path}
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div>
                <CardHeader className="p-0">
                  <CardTitle className="text-lg leading-none">
                    Hubspot
                  </CardTitle>
                  <CardDescription>
                    Send {getCardDescription(path)} data to a Hubspot object.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  <ObjectsChoice form={form} formValues={formValues} />
                  {formValues.hubspot_object_choice
                    ? (() => {
                        switch (formValues.hubspot_object_choice) {
                          case "standard":
                            return (
                              <>
                                <Separator className="my-9" />
                                <MatchingKey
                                  form={form}
                                  companyProperties={
                                    companyProperties?.properties
                                  }
                                />
                              </>
                            )
                          case "custom":
                            return (
                              <>
                                <Separator className="my-9" />
                                <ObjectCheck
                                  form={form}
                                  formValues={formValues}
                                  objectSynced={objectSynced}
                                  setObjectSynced={setObjectSynced}
                                  setProperties={setCustomObjectProperties}
                                />
                                {customObjectProperties ? (
                                  <>
                                    <Separator className="my-9" />
                                    <MatchingKey
                                      form={form}
                                      companyProperties={
                                        companyProperties?.properties
                                      }
                                    />
                                  </>
                                ) : null}

                                {/* Display properties mapping only if key matching has been made on a custom object and object synced */}
                                {objectSynced &&
                                formValues.hubspot_object_name &&
                                formValues.datachimp_hubspot_matching_key
                                  ?.datachimp?.value &&
                                formValues.datachimp_hubspot_matching_key
                                  ?.hubspot?.value ? (
                                  <>
                                    <Separator className="my-9" />
                                    <ObjectsFields
                                      form={form}
                                      path={path}
                                      title="Map Datachimp keys to your Hubspot's custom object properties."
                                      formKey="hubspot_custom_properties_mapping"
                                      formValues={formValues}
                                      properties={customObjectProperties}
                                      description="Select the Datachimp keys you want to send to Hubspot and the corresponding properties in your custom object."
                                    />
                                  </>
                                ) : null}
                              </>
                            )
                        }
                      })()
                    : null}

                  {shouldDisplayCompanyCreation ? (
                    <>
                      {/* Ask for company creation */}
                      <Separator className="my-9" />
                      <CompanyCreationChoice
                        form={form}
                        formValues={formValues}
                      />

                      {formValues.hubspot_company_creation === "yes" ? (
                        <>
                          <Separator className="my-9" />
                          {/* Map Hubspot's company properties */}
                          <ObjectsFields
                            form={form}
                            path={path}
                            title="Map Datachimp keys to your Hubspot's company properties."
                            formKey="hubspot_company_properties_mapping"
                            formValues={formValues}
                            properties={companyProperties?.properties}
                            description="Select the Datachimp keys you want to send to Hubspot and the corresponding properties in your Companies object."
                          />
                        </>
                      ) : null}
                    </>
                  ) : null}

                  {shouldDisplayContactCreation ? (
                    <>
                      {/* Ask for contact creation */}
                      <Separator className="my-9" />
                      <ContactCreationChoice
                        form={form}
                        formValues={formValues}
                      />
                      <Separator className="my-9" />

                      {formValues.hubspot_contact_creation === "yes" ? (
                        <>
                          {/* Map Hubspot's contact properties */}
                          <ObjectsFields
                            form={form}
                            path={path}
                            title="Map Datachimp keys to your Hubspot's contact properties."
                            formKey="hubspot_contact_properties_mapping"
                            formValues={formValues}
                            properties={contactProperties?.properties}
                            description="Select the Datachimp keys you want to send to Hubspot and the corresponding properties in your Contact object."
                            enrichmentProviders={enrichmentProviders}
                          />
                        </>
                      ) : null}
                    </>
                  ) : null}
                </CardContent>

                {/* Display save button on custom vs standard objects */}
                {shouldDisplaySaveButton() && (
                  <CardFooter className="p-0">
                    <Button type="submit">
                      {isSaving ? (
                        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      <span>Save step</span>
                    </Button>
                  </CardFooter>
                )}
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ path, isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">Hubspot</CardTitle>
            <CardDescription>
              Send {getCardDescription(path)} data to a Hubspot object.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="hubspot" setSteps={setSteps} />
      )}
    </Card>
  )
}
