"use client"

import * as React from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { Icons } from "@/components/icons"
import { Alert } from "@/components/ui/alert"

interface WorkflowOperationsProps {
  stepName: string
  setSteps: any
}

export function WorkflowOperations({
  stepName,
  setSteps,
}: WorkflowOperationsProps) {
  const [showDeleteAlert, setShowDeleteAlert] = React.useState<boolean>(false)

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger className="flex h-8 w-8 items-center justify-center rounded-md border transition-colors hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-slate-400 focus-visible:outline-none">
          <Icons.ellipsis className="h-4 w-4" />
          <span className="sr-only">Open</span>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="bg-white">
          <DropdownMenuItem
            className="cursor-pointer hover:bg-slate-100"
            onSelect={() =>
              setSteps((steps) =>
                steps.map((step) => {
                  if (step.type === stepName) {
                    return { ...step, open: true }
                  } else {
                    return step
                  }
                })
              )
            }
          >
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem
            className="cursor-pointer text-red-600 focus:bg-red-100"
            onSelect={() => setShowDeleteAlert(true)}
          >
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <Alert open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <Alert.Content>
          <Alert.Header>
            <Alert.Title>
              Are you sure you want to delete your{" "}
              {stepName[0].toUpperCase() + stepName.slice(1)} step?
            </Alert.Title>
            <Alert.Description>This action cannot be undone.</Alert.Description>
          </Alert.Header>
          <Alert.Footer>
            <Alert.Cancel>Cancel</Alert.Cancel>
            <Alert.Action
              onClick={async (event) => {
                event.preventDefault()

                setSteps((steps) =>
                  steps.filter((step) => step.type !== stepName)
                )
              }}
              className="bg-red-600 focus:ring-red-600"
            >
              <Icons.trash className="mr-2 h-4 w-4" />

              <span>Delete</span>
            </Alert.Action>
          </Alert.Footer>
        </Alert.Content>
      </Alert>
    </>
  )
}
