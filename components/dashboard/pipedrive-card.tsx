"use client"

import * as z from "zod"
import useS<PERSON> from "swr"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { pipedriveSchema } from "@/lib/validations/pipedrive"

import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Separator } from "@/components/ui/separator"
import CompanyCreationChoice from "./pipedrive/company-creation-choice"
// import ContactCreationChoice from "./pipedrive/contact-creation-choice"
import MatchingKey from "@/components/dashboard/pipedrive/matching-key"
import ObjectsFields from "@/components/dashboard/pipedrive/objects-fields"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { Form } from "@/components/react-hook-form/form"
import { fetcher } from "@/lib/utils"

const isMatchingValid = (formValues) =>
  formValues.datachimp_pipedrive_matching_key?.datachimp?.value &&
  formValues.datachimp_pipedrive_matching_key?.pipedrive?.value

const isCompanyCreationValid = (formValues) =>
  formValues.pipedrive_company_creation === "yes" &&
  formValues.pipedrive_company_properties_mapping.length &&
  formValues.pipedrive_company_properties_mapping[0]?.datachimp?.value &&
  formValues.pipedrive_company_properties_mapping[0]?.pipedrive?.value

const isCompanyNotCreationValid = (formValues) =>
  formValues.pipedrive_company_creation === "no"

const isContactCreationValid = (formValues) =>
  formValues.pipedrive_contact_properties_mapping.length &&
  formValues.pipedrive_contact_properties_mapping[0]?.datachimp?.value &&
  formValues.pipedrive_contact_properties_mapping[0]?.pipedrive?.value

const getCardDescription = (path) => {
  switch (path) {
    case "new-hires":
      return "new hires"
    case "job-changes":
      return "job changes"
    case "job-offers":
      return "job offers"
  }
}

export default function PipedriveCard({ path, step, setSteps, stepNumber }) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)
  const [enrichmentProviders, setEnrichmentProviders] = useState()

  async function onSubmit(data: z.infer<typeof pipedriveSchema>) {
    setIsSaving(true)

    const response = await fetch(`/api/${path}/workflow?key=pipedrive`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    setSteps((steps) =>
      steps.map((step) => {
        if (step.type === "pipedrive") {
          return { ...step, open: false }
        } else {
          return step
        }
      })
    )

    router.refresh()

    return toast({
      title: "Your Pipedrive settings have been saved 🎉",
      description: "You can configure the rest of your workflow.",
    })
  }

  const form = useForm<z.infer<typeof pipedriveSchema>>({
    resolver: zodResolver(pipedriveSchema),
    defaultValues: {
      datachimp_pipedrive_matching_key: {
        datachimp: { label: "", value: "" },
        pipedrive: { label: "", value: "" },
      },
      pipedrive_company_creation: "",
      pipedrive_contact_creation: "",
      pipedrive_company_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          pipedrive: { label: "", value: "", type: "standard" },
        },
      ],
      pipedrive_contact_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          pipedrive: { label: "", value: "", type: "standard" },
        },
      ],
    },
  })

  const { data, isLoading } = useSWR(
    `/api/${path}/workflow?key=pipedrive`,
    fetcher
  )

  const { data: enrichment } = useSWR(
    `/api/${path}/workflow?key=enrichment`,
    fetcher
  )

  const { data: companyProperties } = useSWR(
    `/api/pipedrive/properties?object=companies`,
    fetcher
  )

  const { data: contactProperties } = useSWR(
    `/api/pipedrive/properties?object=contacts`,
    fetcher
  )

  const { watch } = form
  const formValues = watch()

  const shouldDisplayCompanyCreation = isMatchingValid(formValues)

  const shouldDisplayContactCreation =
    shouldDisplayCompanyCreation &&
    (isCompanyCreationValid(formValues) ||
      isCompanyNotCreationValid(formValues))

  // Reload data in the form every time the step is edited
  useEffect(() => {
    if (data?.pipedrive_settings) {
      form.reset(JSON.parse(data.pipedrive_settings))
    }
  }, [data, step])

  // Check which are the enrichment providers activated
  useEffect(() => {
    if (enrichment?.enrichment_settings) {
      const parsedProviders = JSON.parse(enrichment.enrichment_settings)
      setEnrichmentProviders(parsedProviders)
    }
  }, [enrichment, step])

  if (isLoading) {
    return (
      <CardItem
        path={path}
        isLoading
        setSteps={setSteps}
        stepNumber={stepNumber}
      />
    )
  }

  console.log("formValues >>>", formValues)

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          path={path}
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div>
                <CardHeader className="p-0">
                  <CardTitle className="text-lg leading-none">
                    Pipedrive
                  </CardTitle>
                  <CardDescription>
                    Send {getCardDescription(path)} data to a Pipedrive object.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  <MatchingKey
                    form={form}
                    companyProperties={companyProperties?.properties}
                  />

                  <>
                    {/* Ask for company creation */}
                    {shouldDisplayCompanyCreation ? (
                      <>
                        <Separator className="my-9" />
                        <CompanyCreationChoice
                          form={form}
                          formValues={formValues}
                        />
                      </>
                    ) : null}

                    {formValues.pipedrive_company_creation === "yes" ? (
                      <>
                        <Separator className="my-9" />
                        {/* Map Pipedrive's company properties */}
                        <ObjectsFields
                          form={form}
                          path={path}
                          title="Map Datachimp keys to your Pipedrive's company properties."
                          formKey="pipedrive_company_properties_mapping"
                          formValues={formValues}
                          properties={companyProperties?.properties}
                          description="Select the Datachimp keys you want to send to Pipedrive and the corresponding properties in your Organizations object."
                        />
                      </>
                    ) : null}
                  </>

                  {shouldDisplayContactCreation ? (
                    <>
                      {/* Map Pipedrive's contact properties */}
                      <Separator className="my-9" />
                      <ObjectsFields
                        form={form}
                        path={path}
                        title="Map Datachimp keys to your Pipedrive's contact properties."
                        formKey="pipedrive_contact_properties_mapping"
                        formValues={formValues}
                        properties={contactProperties?.properties}
                        description="Select the Datachimp keys you want to send to Pipedrive and the corresponding properties in your People object."
                        enrichmentProviders={enrichmentProviders}
                      />
                    </>
                  ) : null}
                </CardContent>

                {/* Display save button */}
                {isContactCreationValid(formValues) && (
                  <CardFooter className="p-0">
                    <Button type="submit">
                      {isSaving ? (
                        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      <span>Save step</span>
                    </Button>
                  </CardFooter>
                )}
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ path, isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">Pipedrive</CardTitle>
            <CardDescription>
              Send {getCardDescription(path)} data to a Pipedrive object.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="pipedrive" setSteps={setSteps} />
      )}
    </Card>
  )
}
