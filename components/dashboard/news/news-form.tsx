"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"

import * as z from "zod"
import { cn } from "@/lib/utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { Icons } from "../../icons"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import MultiSelect from "@/components/dashboard/multi-select"
import Dropzone from "react-dropzone"
import newsCountries from "@/lib/news/newsCountries.json"
import newsTopics from "@/lib/news/newsTopics.json"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormLabel,
  FormDescription,
} from "@/components/react-hook-form/form"
import { Slider } from "@/components/ui/slider"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Create a custom type guard for File
const isFile = (value: any): value is File => {
  return typeof File !== "undefined" && value instanceof File
}

// Create a custom Zod schema for File
const FileSchema = z.custom<File>((value) => isFile(value), {
  message: "Expected a File object",
})

const FormSchema = z
  .object({
    name: z.string().nonempty({ message: "A search name is required" }),
    file: FileSchema.optional(),
    countries: z
      .array(
        z.object({
          label: z.string(),
          value: z.string(),
          language: z.string(),
          type: z.string(),
        })
      )
      .min(1, { message: "You must input at least 1 country" }),
    topics: z
      .array(
        z.object({
          label: z.string(),
          value: z.string(),
        })
      )
      .min(1, { message: "You must input at least 1 topic" }),
    similarity: z.number(),
  })
  .refine((data) => data.file !== undefined, {
    message: "File is required when submitting the form",
    path: ["file"],
  })

function detectDelimiter(line) {
  const commas = line.match(/,/g)?.length || 0
  const semicolons = line.match(/;/g)?.length || 0

  return commas > semicolons ? "," : ";"
}

function checkFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      const textStr = reader.result as string
      const lines = textStr.trim().split("\n")

      if (lines.length < 2) {
        reject("The file is empty.")
        return
      }

      const delimiter = detectDelimiter(lines[0])
      const headers = lines[0].split(delimiter).map((header) =>
        header
          .trim()
          .toLowerCase()
          .replace(/^["']|["']$/g, "")
      )

      const requiredColumns = [
        "company_name",
        "company_domain",
        "company_description",
      ]

      const missingColumns = requiredColumns.filter(
        (col) => !headers.includes(col)
      )

      const unexpectedColumns = headers.filter(
        (header) => ![...requiredColumns].includes(header)
      )

      if (missingColumns.length > 0) {
        reject(
          `The CSV file is missing the following columns: ${missingColumns.join(
            ", "
          )}`
        )
      } else if (unexpectedColumns.length > 0) {
        reject(
          `The CSV file contains unexpected columns: ${unexpectedColumns.join(
            ", "
          )}`
        )
      } else {
        console.log("All required columns are present.")
        resolve(false)
      }
    }

    reader.readAsText(file)
  })
}

function getFileContent(file) {
  if (!file) {
    return null
  }

  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      resolve(reader.result as string)
    }

    reader.onerror = () => {
      reject(new Error("Error reading the file."))
    }

    reader.readAsText(file)
  })
}

interface NewsFormProps {
  searchId: string
  newsConfig: z.infer<typeof FormSchema>
  newsFileContent: any
}

export function NewsForm({
  searchId,
  newsConfig,
  newsFileContent,
}: NewsFormProps) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)
  const [initialFile, setInitialFile] = useState<File | undefined>(undefined)

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: newsConfig?.name || "",
      file: initialFile || undefined,
      countries: newsConfig?.countries || [],
      topics: newsConfig?.topics || [],
      similarity: newsConfig?.similarity || 40,
    },
  })

  // Since File doesn't exist on the server, we make sure that we reference it only on the client
  useEffect(() => {
    if (typeof window !== "undefined" && newsFileContent) {
      setInitialFile(
        new File(
          [newsFileContent],
          `${newsConfig?.name?.toLowerCase().replace(" ", "_")}.csv`,
          { type: "text/csv" }
        )
      )
    }
  }, [newsFileContent, newsConfig?.name])

  // Once initialFile is available, we set it in the form
  useEffect(() => {
    if (initialFile) {
      form.setValue("file", initialFile)
    }
  }, [initialFile, form])

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsSaving(true)

    if (data.file) {
      try {
        await checkFile(data.file)
      } catch (error) {
        setIsSaving(false)
        console.log(error)
        return toast({
          title: "CSV file error 🚨",
          description: error as string,
        })
      }
    }

    const fileContent = await getFileContent(data.file)

    const response = await fetch(`/api/news/settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        searchId: searchId,
        name: data.name,
        countries: data.countries,
        topics: data.topics,
        similarity: data.similarity,
        fileContent: fileContent,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    router.refresh()

    toast({
      title: "Your search has been saved 🎉",
      description: "Make those news rain!",
    })

    router.push("/dashboard/news/search")
  }

  return (
    <>
      <div className="flex justify-between">
        <div>
          <h2 className="text-md mb-1 font-semibold">News</h2>
          <p className="mb-6 text-sm text-neutral-500">
            Which kind of online news do you want to track?
          </p>
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="secondary">╳</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Do you want to leave?</AlertDialogTitle>
              <AlertDialogDescription>
                Your search settings won&apos;t be saved if it&apos;s been
                edited.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => router.push("/dashboard/news/search")}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="mb-32 w-4/5 space-y-8"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Search name</FormLabel>
                </div>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Sales / Business"
                    {...field}
                  />
                </FormControl>
                <FormDescription>Give a name to your search.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="file"
            render={({ field }) => (
              <FormItem>
                <div>
                  <div className="mb-1 flex items-center">
                    <span className="mr-1 inline-block h-[19px] text-red-500">
                      *
                    </span>
                    <FormLabel>Your list of accounts</FormLabel>
                  </div>
                  <p className="mb-6 text-sm text-neutral-500">
                    company_name, company_description, company_domain
                  </p>
                </div>
                <FormControl>
                  <>
                    <Dropzone
                      onDrop={(acceptedFiles) =>
                        form.setValue("file", acceptedFiles[0])
                      }
                      accept={{
                        "text/csv": [".csv"],
                      }}
                      disabled={!!formValues.file}
                    >
                      {({
                        getRootProps,
                        getInputProps,
                        isDragAccept,
                        isDragReject,
                      }) => (
                        <section>
                          <div
                            {...getRootProps()}
                            className={cn(
                              "flex h-64 transform flex-col items-center justify-center rounded-md border border-dashed bg-slate-50 transition-all ease-in-out",
                              isDragAccept && "border-blue-500 bg-blue-50",
                              isDragReject && "border-red-500 bg-red-50",
                              formValues.file
                                ? "cursor-no-drop"
                                : "cursor-pointer hover:bg-slate-100"
                            )}
                          >
                            <Input type="file" {...getInputProps()} />
                            {!formValues.file ? (
                              <>
                                <Icons.download className="mb-2 h-6 w-6 text-neutral-500" />
                                <p className="mb-6 text-sm text-neutral-500">
                                  Drag and drop or click to import your CSV
                                  file.
                                </p>
                              </>
                            ) : (
                              <>
                                <Icons.slash className="mb-2 h-6 w-6 text-neutral-500" />
                                <p className="mb-6 text-sm text-neutral-500">
                                  You can import 1 file max.
                                </p>
                              </>
                            )}
                          </div>
                        </section>
                      )}
                    </Dropzone>

                    {formValues.file ? (
                      <div className="inline-flex h-auto items-center rounded-md bg-blue-100 px-[6px] py-1 text-sm font-normal text-blue-600 focus:outline-none">
                        <Icons.page className="h-4 w-4" />
                        <div className="mx-1 text-justify">
                          {formValues.file.name}
                        </div>
                        <button
                          type="button"
                          onClick={() => form.setValue("file", undefined)}
                        >
                          <Icons.close className="h-3 w-3" />
                        </button>
                      </div>
                    ) : null}
                  </>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="countries"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Countries</FormLabel>
                </div>
                <FormControl>
                  <MultiSelect
                    item="country"
                    options={newsCountries}
                    placeholder="United-States"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Select one or multiple countries.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="topics"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Topics</FormLabel>
                </div>
                <FormControl>
                  <MultiSelect
                    item="topic"
                    options={newsTopics}
                    placeholder="Layoff"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Select one or multiple topics.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="similarity"
            render={({ field }) => (
              <FormItem>
                <div className="mb-1 flex items-center">
                  <Label className="mr-1">Similarity</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild className="cursor-pointer">
                        <Icons.info className="h-3.5 w-3.5" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <span>
                          To which extent the article is talking about the
                          company.
                        </span>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <FormControl>
                  <Slider
                    className="w-[50%]"
                    onValueCommit={(value) =>
                      form.setValue("similarity", value[0])
                    }
                    defaultValue={[formValues.similarity]}
                    max={100}
                    step={1}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="mt-6">
            {isSaving ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            <span>Save</span>
          </Button>
        </form>
      </Form>
    </>
  )
}
