"use client"

import React, { forwardRef, ForwardRefRenderFunction } from "react"
import { ControllerRenderProps, FieldValues } from "react-hook-form"

import { cn } from "@/lib/utils"

import { Icons } from "@/components/icons"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"

interface InputWithBadgesProps
  extends ControllerRenderProps<FieldValues, string> {
  allowExclusion?: boolean
}

const InputWithBadges: ForwardRefRenderFunction<
  HTMLInputElement,
  InputWithBadgesProps
> = ({ value, onChange, allowExclusion }, ref) => {
  const [inputValue, setInputValue] = React.useState("")

  const handleInputChange = (e) => {
    setInputValue(e.target.value)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && inputValue.trim() !== "") {
      e.preventDefault()
      const newEntry = inputValue.trim()
      if (
        !value.some(
          (entry) => entry.value.toLowerCase() === newEntry.toLowerCase()
        )
      ) {
        onChange([
          ...value,
          { label: newEntry, value: newEntry, excluded: false },
        ])
        setInputValue("")
      }
    }
  }

  const removeBadge = (index) => {
    onChange(value.filter((_, i) => i !== index))
  }

  const toggleExclusion = (index) => {
    onChange(
      value.map((entry, i) =>
        i === index ? { ...entry, excluded: !entry.excluded } : entry
      )
    )
  }

  return (
    <div ref={ref}>
      <div
        className={cn("mb-2 flex flex-wrap gap-2", !value.length && "hidden")}
      >
        {value.map((entry, index) => (
          <Badge
            key={index}
            variant="secondary"
            className={`rounded-md text-sm font-normal ${
              allowExclusion && entry.excluded
                ? "bg-red-100 text-red-600 hover:bg-red-100/80"
                : "bg-green-100 text-green-600 hover:bg-green-100/80"
            }`}
          >
            {allowExclusion && (
              <button
                type="button"
                className="focus:shadow-outline mr-1 outline-none"
                onClick={() => toggleExclusion(index)}
              >
                {!entry.excluded ? (
                  <Icons.exclude className="h-3 w-3" />
                ) : (
                  <Icons.include className="h-3 w-3" />
                )}
              </button>
            )}
            {entry.label}
            <button
              type="button"
              className="ml-1 outline-none"
              onClick={() => removeBadge(index)}
            >
              <Icons.close className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>
      <Input
        type="text"
        placeholder="Type and press Enter..."
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
      />
    </div>
  )
}

export default forwardRef(InputWithBadges)
