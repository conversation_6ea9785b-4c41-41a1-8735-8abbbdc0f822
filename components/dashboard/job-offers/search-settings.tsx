"use client"

import * as z from "zod"
import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"

import { useForm } from "react-hook-form"
import { useRout<PERSON> } from "next/navigation"
import { useState } from "react"
import { useToast } from "@/hooks/use-toast"

import { Icons } from "@/components/icons"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import MultiSelect from "@/components/dashboard/multi-select"
import Intents from "@/components/dashboard/job-offers/intents"
import InputWithBadges from "@/components/dashboard/input-badges"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  <PERSON>ertDialogHead<PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
  <PERSON>ertDialog<PERSON>rigger,
} from "@/components/ui/alert-dialog"

import linkedinSizes from "@/lib/linkedin-sizes.json"
import linkedinCountries from "@/lib/linkedin-countries.json"
import companyCountries from "@/lib/company-countries.json"
import linkedinIndustries from "@/lib/linkedin-industries.json"
import fundraisingRounds from "@/lib/fundraising-rounds.json"

const saas = [
  { label: "Yes", value: true },
  { label: "No", value: false },
]

const type = [
  { label: "B2B", value: "B2B" },
  { label: "B2C", value: "B2C" },
]

const businessModels = [
  { label: "Subscription-based", value: "subscription" },
  { label: "Non-subscription-based", value: "no-subscription" },
]

const formSchema = z.object({
  name: z.string().trim().nonempty({ message: "A search name is required" }),
  titles: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
        excluded: z.boolean(),
      })
    )
    .min(1, { message: "You must input at least 1 title" }),
  countries: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .min(1, { message: "You must input at least 1 country" }),
  companyCountries: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  industries: z
    .array(
      z.object({
        label: z.string(),
        value: z.number(),
        excluded: z.boolean(),
      })
    )
    .optional(),
  recruitmentFirmsExclusion: z.boolean().optional(),
  companySizes: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  saas: z
    .array(
      z.object({
        label: z.string(),
        value: z.boolean(),
      })
    )
    .optional(),
  type: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  businessModel: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  fundraisingRounds: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  tools: z.object({ label: z.string(), value: z.string() }).array().optional(),
  toolsFilter: z.boolean().optional(),
  intents: z
    .object({
      id: z.string(),
      label: z.string(),
      prompt: z
        .string()
        .max(250, { message: "Your prompt can be 250 characters max" }),
    })
    .array()
    .optional(),
  intentsFilter: z.boolean().optional(),
})

type FormData = z.infer<typeof formSchema>

interface SearchSettingsProps {
  searchId: string
  clientId: string
  nameConfig: string | null | undefined
  jobOffersConfig: z.infer<typeof formSchema>
}

export function SearchSettings({
  searchId,
  clientId,
  jobOffersConfig,
  nameConfig,
}: SearchSettingsProps) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: nameConfig || "",
      titles: jobOffersConfig?.titles || [],
      countries: jobOffersConfig?.countries || [],
      companyCountries: jobOffersConfig?.companyCountries || [],
      industries: jobOffersConfig?.industries || [],
      recruitmentFirmsExclusion:
        jobOffersConfig?.recruitmentFirmsExclusion || false,
      companySizes: jobOffersConfig?.companySizes || [],
      saas: jobOffersConfig?.saas || [],
      type: jobOffersConfig?.type || [],
      businessModel: jobOffersConfig?.businessModel || [],
      fundraisingRounds: jobOffersConfig?.fundraisingRounds || [],
      tools: jobOffersConfig?.tools || [],
      toolsFilter: jobOffersConfig?.toolsFilter || false,
      intents: jobOffersConfig?.intents || [],
      intentsFilter: jobOffersConfig?.intentsFilter || false,
    },
  })

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: FormData) {
    setIsSaving(true)

    const response = await fetch(`/api/job-offers/settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        searchId: searchId,
        name: data.name,
        titles: data.titles,
        countries: data.countries,
        companyCountries: data.companyCountries,
        industries: data.industries,
        companySizes: data.companySizes,
        type: data.type,
        saas: data.saas,
        businessModel: data.businessModel,
        tools: data.tools,
        toolsFilter: data.toolsFilter,
        intents: data.intents,
        intentsFilter: data.intentsFilter,
        recruitmentFirmsExclusion: data.recruitmentFirmsExclusion,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    router.refresh()

    toast({
      title: "Your search has been saved 🎉",
      description: "Make those leads rain!",
    })

    router.push("/dashboard/job-offers/search")
  }

  return (
    <>
      <div className="mb-10 flex justify-between">
        <div>
          <h2 className="text-md mb-1 font-semibold">Job postings</h2>
          <p className="text-sm text-neutral-500">
            Which job postings do you want to track?
          </p>
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="secondary">╳</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Do you want to leave?</AlertDialogTitle>
              <AlertDialogDescription>
                Your search settings won&apos;t be saved if it&apos;s been
                edited.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => router.push("/dashboard/job-offers/search")}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-4/5 space-y-8"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Search name</FormLabel>
                </div>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Sales / Business"
                    {...field}
                  />
                </FormControl>
                <FormDescription>Give a name to your search.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="titles"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Titles</FormLabel>
                </div>
                <FormControl>
                  <InputWithBadges {...field} allowExclusion />
                </FormControl>
                <FormDescription>
                  Add job titles and toggle their inclusion/exclusion by
                  clicking on the badge left button.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="countries"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Jobs locations</FormLabel>
                </div>
                <FormControl>
                  <MultiSelect
                    item="country"
                    options={linkedinCountries.filter((c) => !c.region)}
                    placeholder="United-States"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  The country where the job is located.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companyCountries"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Company locations</FormLabel>
                </div>
                <FormControl>
                  <MultiSelect
                    item="country"
                    options={companyCountries}
                    placeholder="United-States"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  The country where the company has its headquarters.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="industries"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Industries</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="industry"
                    allowExclusion
                    options={linkedinIndustries}
                    placeholder="Software Development"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No industry specified equals all industries.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="recruitmentFirmsExclusion"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked === true)
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Exclude recruitment firms</FormLabel>
                  <FormDescription>
                    Remove job posts published by recruitment and staffing
                    companies.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companySizes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company sizes</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company size"
                    options={linkedinSizes}
                    placeholder="201 - 500"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No size specified equals all sizes.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="saas"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company is a Saas</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company type"
                    options={saas}
                    placeholder="Yes"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No option specified equals both.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company type</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company type"
                    options={type}
                    placeholder="B2B"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No option specified equals both.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="businessModel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company business model</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company business model"
                    options={businessModels}
                    placeholder="Subscription-based"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No option specified equals both.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="fundraisingRounds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company last fundraising round</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company fundraising rounds"
                    options={fundraisingRounds}
                    placeholder="Seed"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  This filter will target companies with a specific last
                  fundraising round.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <div>
            <Separator className="my-10" />
          </div>
          <FormField
            control={form.control}
            name="tools"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tools</FormLabel>
                <FormControl>
                  <InputWithBadges {...field} />
                </FormControl>
                <FormDescription>
                  This will highlight specific tools mentioned in a job
                  description (competitors, technologies...).
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="toolsFilter"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked === true)
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Filter by mentions of tools</FormLabel>
                  <FormDescription>
                    Only get job offers that mention these specific tools.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          <div>
            <Separator className="my-10" />
          </div>
          <FormField
            control={form.control}
            name="intents"
            render={({ field }) => (
              <FormItem>
                <div className="mb-4 space-y-1 leading-none">
                  <FormLabel>Intents extractor</FormLabel>
                  <FormDescription>
                    Use a ChatGPT prompt to specify what kind of sentences you
                    want to extract from job posts.
                  </FormDescription>
                </div>
                <FormControl>
                  <Intents {...field} clientId={clientId} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="intentsFilter"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked === true)
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Filter by mentions of intents</FormLabel>
                  <FormDescription>
                    Only get job offers that mention at least one intent.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />
          <Button type="submit">
            {isSaving ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            <span>Save</span>
          </Button>
        </form>
      </Form>
    </>
  )
}
