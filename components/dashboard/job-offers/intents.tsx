import React, { useEffect, useState, forwardRef, useRef } from "react"

import { Icons } from "@/components/icons"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/text-area"
import { useFieldArray, useForm, Control, useWatch } from "react-hook-form"

interface Intent {
  id: string
  label: string
  prompt: string
}

interface IntentsProps {
  value: Intent[]
  onChange: (value: Intent[]) => void
  clientId?: string
}

let nextId = 0
const generateId = () => `intent-${nextId++}`

const Intents = forwardRef<HTMLDivElement, IntentsProps>(
  ({ value, onChange, clientId }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null)

    const { control } = useForm<{ intents: Intent[] }>({
      defaultValues: {
        intents:
          value.length > 0
            ? value
            : [{ id: generateId(), label: "", prompt: "" }],
      },
    })

    const { fields, append, remove } = useFieldArray({
      control,
      name: "intents",
    })

    const watchFieldArray = useWatch({
      control,
      name: "intents",
      defaultValue: fields,
    })
    const controlledFields = fields.map((field, index) => ({
      ...field,
      ...watchFieldArray[index],
    }))

    const removeIntent = (index: number) => {
      remove(index)
    }

    useEffect(() => {
      onChange(watchFieldArray)
    }, [watchFieldArray, onChange])

    const addIntent = () => {
      const maxIntents = clientId === "cm1kja51x0000mn0cf3db8j28" ? 10 : 3
      if (fields.length < maxIntents) {
        append({ id: generateId(), label: "", prompt: "" })
      }
    }

    const toSnakeCase = (string: string) => {
      return string.toLowerCase().replace(/\s+/g, "_")
    }

    const getOutput = (intents: Intent[]) => {
      const output: Record<string, string[]> = {}
      intents.forEach((intent) => {
        if (intent.label) {
          output[toSnakeCase(intent.label)] = [
            "You will be in charge of...",
            "You'll implement...",
            "You'll benchmark...",
          ]
        }
      })
      return output
    }

    const maxIntents = clientId === "cm1kja51x0000mn0cf3db8j28" ? 10 : 3

    return (
      <div ref={containerRef} className="space-y-4 ">
        {controlledFields.map((field, index) => (
          <IntentCard
            key={index}
            intent={field}
            index={index}
            control={control}
            remove={() => removeIntent(index)}
          />
        ))}
        {fields.length < maxIntents && (
          <div className="flex justify-center">
            <Button
              onClick={(e) => {
                e.preventDefault()
                addIntent()
              }}
              variant="secondary"
            >
              <Icons.add className="h-4 w-4" />
            </Button>
          </div>
        )}

        <div className="mt-6">
          <Label className="mb-2 inline-block">Output example</Label>
          <pre className="overflow-x-auto rounded-lg bg-gray-100 p-2 text-sm">
            {JSON.stringify(getOutput(watchFieldArray), null, 2)}
          </pre>
        </div>
      </div>
    )
  }
)

Intents.displayName = "Intents"

interface IntentCardProps {
  intent: Intent
  index: number
  control: Control<{ intents: Intent[] }>
  remove: (index: number) => void
}

const IntentCard = forwardRef<HTMLDivElement, IntentCardProps>(
  ({ intent, index, control, remove }, ref) => {
    const cardRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      if (cardRef && cardRef.current) {
        cardRef.current.style.opacity = "0"
        cardRef.current.style.transform = "translateY(20px)"
        setTimeout(() => {
          cardRef.current!.style.opacity = "1"
          cardRef.current!.style.transform = "translateY(0)"
        }, 50)
      }
    }, [ref])

    return (
      <div
        ref={cardRef}
        className="relative rounded-lg border p-6 transition-all duration-300 ease-in-out"
      >
        <Label className="mb-2 block">Intent Label</Label>
        <Input
          {...control.register(`intents.${index}.label`)}
          placeholder="e.g., financial tools implementation"
          className="mb-4"
        />
        <Label className="mb-2 block">Extraction Prompt</Label>
        <Textarea
          {...control.register(`intents.${index}.prompt`)}
          placeholder={`e.g., Extract sentences describing ${
            intent?.label?.toLowerCase() || "..."
          }`}
          maxLength={250}
        />
        {index > 0 && (
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.preventDefault()
              remove(index)
            }}
            className="absolute -right-14 top-1/2 -translate-y-1/2"
          >
            <Icons.trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    )
  }
)

IntentCard.displayName = "IntentCard"

export default Intents
