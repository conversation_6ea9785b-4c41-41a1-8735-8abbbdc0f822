"use client"

import * as z from "zod"
import useS<PERSON> from "swr"
import { fetcher } from "@/lib/utils"
import { useState, useEffect, useCallback } from "react"
import { useToast } from "@/hooks/use-toast"
import { useForm } from "react-hook-form"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/icons"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
  CardContent,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"

const FormSchema = z.object({
  webhooks: z
    .array(
      z.object({
        searchId: z.number(),
        searchName: z.string(),
        url: z.string().url("Must be a valid URL"),
      })
    )
    .min(1, { message: "You must input at least 1 webhook" }),
})

export default function WebhookCard({ step, setSteps, path, stepNumber }) {
  const { toast } = useToast()

  const { data: searches, mutate } = useSWR(`/api/${path}/webhook`, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  })

  const [isSaving, setIsSaving] = useState(false)

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      webhooks: [{ searchId: undefined, searchName: "", url: "" }],
    },
  })

  const resetFormWithData = useCallback(
    (data) => {
      const formattedWebhooks = data.map((webhook) => ({
        searchId: webhook.id,
        searchName: webhook.name,
        url: webhook.webhook_url || "",
      }))
      form.reset({ webhooks: formattedWebhooks })
    },
    [form]
  )

  const { watch } = form
  const formValues = watch()

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    setIsSaving(true)

    try {
      const response = await fetch(`/api/${path}/webhook`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ data }),
      })

      if (!response.ok) {
        throw new Error(await response.text())
      }

      // Optimistically update the local data
      const updatedSearches = searches.map((search) => {
        const updatedWebhook = data.webhooks.find(
          (w) => w.searchId === search.id
        )
        return updatedWebhook
          ? { ...search, webhook_url: updatedWebhook.url }
          : search
      })

      // Update SWR cache and revalidate
      await mutate(updatedSearches, false)

      setSteps((prevSteps) =>
        prevSteps.map((s) => (s.type === "webhook" ? { ...s, open: false } : s))
      )

      toast({
        title: "Your webhook settings have been saved 🎉",
        description: "You can configure the rest of your workflow.",
      })
    } catch (error) {
      toast({
        title: "Uh oh! Something went wrong 🚨",
        description: error.message,
      })
    } finally {
      setIsSaving(false)
    }
  }

  useEffect(() => {
    if (searches?.length) {
      resetFormWithData(searches)
    }
  }, [searches, resetFormWithData])

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div className="w-[600px]">
                <CardHeader className="p-0">
                  <CardTitle className="text-lg leading-none">
                    Webhook Configuration
                  </CardTitle>
                  <CardDescription>
                    Set webhook URLs for each search.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  {formValues.webhooks?.map((webhook, i: number) => (
                    <div
                      className="mb-4 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)] items-center gap-3"
                      key={i}
                    >
                      <div>
                        <FormField
                          control={form.control}
                          name={`webhooks.${i}.searchName`}
                          render={({ field }) => {
                            return (
                              <FormControl>
                                <FormItem className="flex flex-col space-y-2">
                                  <Input
                                    type="text"
                                    {...field}
                                    disabled
                                    className="bg-gray-100"
                                  />
                                  <FormMessage className="text-[13px]" />
                                </FormItem>
                              </FormControl>
                            )
                          }}
                        />
                      </div>
                      <div className="h-full">
                        <div className="flex h-10 items-center">
                          <Icons.arrowRight className="h-[20px] w-[20px] text-sm text-slate-300" />
                        </div>
                      </div>
                      <div>
                        <FormField
                          control={form.control}
                          name={`webhooks.${i}.url`}
                          render={({ field }) => {
                            return (
                              <FormControl>
                                <FormItem className="flex flex-col space-y-2">
                                  <Input
                                    type="text"
                                    placeholder="Webhook url"
                                    onChange={(e) => {
                                      form.setValue(`webhooks.${i}`, {
                                        ...webhook,
                                        searchId: webhook.searchId,
                                        url: e.target.value,
                                      })
                                    }}
                                    defaultValue={field.value}
                                  />
                                  <FormMessage className="text-[13px]" />
                                </FormItem>
                              </FormControl>
                            )
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </CardContent>
                <CardFooter className="p-0">
                  <Button type="submit">
                    {isSaving ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    <span>Save step</span>
                  </Button>
                </CardFooter>
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">Webhook</CardTitle>
            <CardDescription>
              Choose which webhook you want to send your data to.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="webhook" setSteps={setSteps} />
      )}
    </Card>
  )
}
