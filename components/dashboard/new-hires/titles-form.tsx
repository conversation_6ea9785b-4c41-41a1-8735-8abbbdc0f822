"use client"

import * as z from "zod"
import { zodResolver } from "@hookform/resolvers/zod"

import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { useToast } from "@/hooks/use-toast"

import { Icons } from "@/components/icons"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import MultiSelect from "@/components/dashboard/multi-select"
import InputWithBadges from "@/components/dashboard/input-badges"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import linkedinSizes from "@/lib/linkedin-sizes.json"
import linkedinIndustries from "@/lib/linkedin-industries.json"
import linkedinCountries from "@/lib/linkedin-countries.json"
import fundraisingRounds from "@/lib/fundraising-rounds.json"

const saas = [
  { label: "Yes", value: true },
  { label: "No", value: false },
]

const type = [
  { label: "B2B", value: "B2B" },
  { label: "B2C", value: "B2C" },
]

const businessModels = [
  { label: "Subscription-based", value: "subscription" },
  { label: "Non-subscription-based", value: "no-subscription" },
]

const formSchema = z.object({
  name: z.string().trim().nonempty({ message: "A search name is required" }),
  titles: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
        excluded: z.boolean(),
      })
    )
    .min(1, { message: "You must input at least 1 title" }),
  countries: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .min(1, { message: "You must input at least 1 country" }),
  companyCountries: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
        code: z.string().optional(),
      })
    )
    .optional(),
  industries: z
    .array(
      z.object({
        label: z.string(),
        value: z.number(),
        excluded: z.boolean(),
      })
    )
    .optional(),
  companySizes: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  saas: z
    .array(
      z.object({
        label: z.string(),
        value: z.boolean(),
      })
    )
    .optional(),
  type: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  businessModel: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
  fundraisingRounds: z
    .array(
      z.object({
        label: z.string(),
        value: z.string(),
      })
    )
    .optional(),
})

type FormData = z.infer<typeof formSchema>

interface TitlesFormProps {
  searchId: string
  nameConfig: string | null | undefined
  titlesConfig: z.infer<typeof formSchema>
}

export function TitlesForm({
  searchId,
  nameConfig,
  titlesConfig,
}: TitlesFormProps) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: nameConfig || "",
      titles: titlesConfig?.titles || [],
      countries: titlesConfig?.countries || [],
      companyCountries: titlesConfig?.companyCountries || [],
      industries: titlesConfig?.industries || [],
      companySizes: titlesConfig?.companySizes || [],
      saas: titlesConfig?.saas || [],
      type: titlesConfig?.type || [],
      businessModel: titlesConfig?.businessModel || [],
      fundraisingRounds: titlesConfig?.fundraisingRounds || [],
    },
  })

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: FormData) {
    setIsSaving(true)

    const response = await fetch(`/api/new-hires/settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        searchId: searchId,
        name: data.name,
        titles: data.titles,
        countries: data.countries,
        companyCountries: data.companyCountries,
        industries: data.industries,
        companySizes: data.companySizes,
        type: data.type,
        saas: data.saas,
        businessModel: data.businessModel,
        fundraisingRounds: data.fundraisingRounds,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    router.refresh()

    toast({
      title: "Your search has been saved 🎉",
      description: "Make those leads rain!",
    })

    router.push("/dashboard/new-hires/search")
  }

  return (
    <>
      <div className="mb-10 flex justify-between">
        <div>
          <h2 className="text-md mb-1 font-semibold">Persona</h2>
          <p className="text-sm text-neutral-500">
            What persona do you want to track new hires for?
          </p>
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="secondary">╳</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Do you want to leave?</AlertDialogTitle>
              <AlertDialogDescription>
                Your search settings won&apos;t be saved if it&apos;s been
                edited.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => router.push("/dashboard/new-hires/search")}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-4/5 space-y-8"
        >
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Search name</FormLabel>
                </div>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Sales / Business"
                    {...field}
                  />
                </FormControl>
                <FormDescription>Give a name to your search.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="titles"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Titles</FormLabel>
                </div>
                <FormControl>
                  <InputWithBadges {...field} allowExclusion />
                </FormControl>
                <FormDescription>
                  Add job titles and toggle their inclusion/exclusion by
                  clicking on the badge left button.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="countries"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center">
                  <span className="mr-1 inline-block h-[19px] text-red-500">
                    *
                  </span>
                  <FormLabel>Countries</FormLabel>
                </div>
                <FormControl>
                  <MultiSelect
                    item="country"
                    options={linkedinCountries}
                    placeholder="United-States"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  The country where the newly hired employee will be based.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companyCountries"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company headquarters locations</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="country"
                    options={linkedinCountries.filter((c) => !c.region)}
                    placeholder="United-States"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  The country where the company has its headquarters.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="industries"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Industries</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="industry"
                    allowExclusion={true}
                    options={linkedinIndustries}
                    placeholder="Software Development"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No industry specified equals all industries.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="companySizes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company sizes</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company size"
                    options={linkedinSizes}
                    placeholder="201 - 500"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No size specified equals all sizes.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="saas"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company is a Saas</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company type"
                    options={saas}
                    placeholder="Yes"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No option specified equals both.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company type</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company type"
                    options={type}
                    placeholder="B2B"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No option specified equals both.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="businessModel"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company business model</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company business model"
                    options={businessModels}
                    placeholder="Subscription-based"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  No option specified equals both.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="fundraisingRounds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company last fundraising round</FormLabel>
                <FormControl>
                  <MultiSelect
                    item="company fundraising rounds"
                    options={fundraisingRounds}
                    placeholder="Seed"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  This filter will target companies with a specific last
                  fundraising round.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit">
            {isSaving ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            <span>Save</span>
          </Button>
        </form>
      </Form>
    </>
  )
}
