"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

import * as z from "zod"
import { zodResolver } from "@hookform/resolvers/zod"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Icons } from "../icons"

const formSchema = z.object({
  webhookUrl: z
    .string()
    .url("Please enter a valid URL")
    .min(1, "Webhook URL is required"),
})

type FormData = z.infer<typeof formSchema>

interface WebhookFormProps {
  path: string
}

export function WebhookForm({ path }: WebhookFormProps) {
  const { toast } = useToast()
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      webhookUrl: "",
    },
  })

  async function onSubmit(data: FormData) {
    setIsSaving(true)
    try {
      const response = await fetch(`/api/${path}/webhook`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ webhookUrl: data.webhookUrl }),
      })

      if (!response?.ok) throw new Error()

      router.refresh()
      toast({
        title: "Your webhook has been saved 🎉",
        description: `Your data will be sent to: ${data.webhookUrl}`,
      })
      form.reset()
    } catch (err) {
      toast({
        title: "Uh oh! Something went wrong 🚨",
        description: "Your webhook was not saved. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="webhookUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Webhook URL</FormLabel>
              <FormControl>
                <Input
                  placeholder="https://api.example.com/webhook"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isSaving}>
          {isSaving && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          <span>{isSaving ? "Saving..." : "Save Webhook"}</span>
        </Button>
      </form>
    </Form>
  )
}
