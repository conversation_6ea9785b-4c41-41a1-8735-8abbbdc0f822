"use client"

import * as z from "zod"
import { schedulerSchema } from "@/lib/validations/scheduler"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { SchedulerOperations } from "@/components/dashboard/scheduler-operations"
import { Icons } from "@/components/icons"
import parser from "cron-parser"

import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"

interface SchedulerItemProps {
  scheduler: z.infer<typeof schedulerSchema>
}

const getSchedulerState = (state: string) => {
  if (state === "ENABLED") {
    return true
  }

  return false
}

export function SchedulerItem({ scheduler }: SchedulerItemProps) {
  const { toast } = useToast()
  const [enabled, setEnabled] = useState(getSchedulerState(scheduler.state))
  const [parsedCron, setParsedCron] = useState("")

  useEffect(() => {
    const interval = parser.parseExpression(scheduler.schedule, {
      tz: scheduler.timezone,
    })
    const date = new Date(interval.next().toString())

    const dayOfWeek = date.toLocaleString("en-US", { weekday: "short" })
    const month = date.toLocaleString("en-US", { month: "short" })
    const dayOfMonth = date.toLocaleString("en-US", { day: "numeric" })
    const time = date.toLocaleString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    })
    const [hour, minutes, period] = time.split(/[:\s]+/)

    setParsedCron(
      `${dayOfWeek} ${month} ${dayOfMonth} at ${hour}.${minutes} ${period}`
    )
  }, [scheduler])

  const updateScheduler = async () => {
    setEnabled(!enabled)

    const response = await fetch(`/api/${scheduler.path}/cron`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        update: enabled ? "pause" : "resume",
      }),
    })

    if (!response?.ok) {
      setEnabled(!enabled)

      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your scheduler was not updated. Please try again.",
      })
    }
  }

  const getSchedulerTitle = () => {
    switch (scheduler.path) {
      case "job-offers":
        return "Job offers scheduler"
      case "new-hires":
        return "New hires scheduler"
      default:
        return "Insight scheduler"
    }
  }

  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <span className="text-sm font-semibold">{getSchedulerTitle()}</span>
        <div className="flex items-center text-sm text-slate-600">
          <Icons.clock className="mr-1 h-4 w-4" />
          <p>
            Next run on {parsedCron} (<i>{scheduler.timezone}</i>)
          </p>
        </div>
      </div>
      <div className="flex items-center gap-6">
        {enabled ? (
          <div className="inline-flex h-6 items-center justify-center rounded-md bg-green-100 px-[6px] py-0.5 text-xs font-normal text-green-600 ">
            Enabled
          </div>
        ) : (
          <div className="inline-flex h-6 items-center justify-center rounded-md bg-yellow-100 px-[6px] py-0.5 text-xs font-normal text-yellow-600 ">
            Disabled
          </div>
        )}
        <Switch checked={enabled} onClick={updateScheduler} />
        <SchedulerOperations path={scheduler.path} />
      </div>
    </div>
  )
}

SchedulerItem.Skeleton = function SchedulerItemSkeleton() {
  return (
    <div className="p-4">
      <Skeleton className="h-5 w-2/5" />
    </div>
  )
}
