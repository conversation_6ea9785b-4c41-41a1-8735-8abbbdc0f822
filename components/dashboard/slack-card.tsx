"use client"

import * as z from "zod"
import useS<PERSON> from "swr"
import { fetcher } from "@/lib/utils"
import { useState, useEffect, useCallback } from "react"
import { useToast } from "@/hooks/use-toast"
import { Check, ChevronsUpDown } from "lucide-react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/icons"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
  CardContent,
} from "@/components/ui/card"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"

import { cn } from "@/lib/utils"

const FormSchema = z.object({
  slackOwnerNotification: z.enum(["yes", "no"]).optional(),
  slackMappings: z
    .array(
      z.object({
        searchId: z.number(),
        searchName: z.string(),
        slackChannel: z.string(),
      })
    )
    .min(1, { message: "You must input at least 1 Slack channel mapping" }),
})

export default function SlackCard({ step, steps, setSteps, path, stepNumber }) {
  const router = useRouter()
  const { toast } = useToast()

  const { data: channels, isLoading: channelIsLoading } = useSWR(
    `/api/slack/channels`,
    fetcher
  )
  const { data: searches, isLoading: searchesIsLoading } = useSWR(
    `/api/${path}/slack`,
    fetcher
  )

  const [isSaving, setIsSaving] = useState(false)

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      slackOwnerNotification: "no",
      slackMappings: [
        {
          searchId: undefined,
          searchName: "",
          slackChannel: "",
        },
      ],
    },
  })

  const resetFormWithData = useCallback(
    (searches) => {
      const slackOwnerNotification = JSON.parse(
        searches[0].slack_settings
      )?.slack_owner_notification
      const formattedMappings = searches.map((search) => ({
        searchId: search.id,
        searchName: search.name,
        slackChannel: JSON.parse(search.slack_settings)?.slack_channel || "",
      }))
      form.reset({ slackMappings: formattedMappings, slackOwnerNotification })
    },
    [form]
  )

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsSaving(true)

    try {
      const response = await fetch(`/api/${path}/slack`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ data }),
      })

      if (!response.ok) {
        throw new Error(await response.text())
      }

      setSteps((prevSteps) =>
        prevSteps.map((s) => (s.type === "slack" ? { ...s, open: false } : s))
      )

      router.refresh()

      toast({
        title: "Your Slack settings have been saved 🎉",
        description: "You can configure the rest of your workflow.",
      })
    } catch (error) {
      toast({
        title: "Uh oh! Something went wrong 🚨",
        description: error.message,
      })
    } finally {
      setIsSaving(false)
    }
  }

  const ownerNotificationYes = formValues.slackOwnerNotification === "yes"

  const slackIndex = steps.findIndex((item) => item.type === "slack")
  const hubspotIndex = steps.findIndex((item) => item.type === "hubspot")
  const salesforceIndex = steps.findIndex((item) => item.type === "salesforce")

  const isHubspotBeforeSlack = hubspotIndex < slackIndex
  const isHubspotExisting = steps.some((step) => step.type === "hubspot")
  const isSalesforceBeforeSlack = salesforceIndex < slackIndex
  const isSalesforceExisting = steps.some((step) => step.type === "salesforce")

  useEffect(() => {
    if (searches?.length) {
      resetFormWithData(searches)
    }
  }, [searches, resetFormWithData])

  if (channelIsLoading || searchesIsLoading) {
    return <CardItem isLoading setSteps={setSteps} stepNumber={stepNumber} />
  }

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div className="w-[600px]">
                <CardHeader className="p-0">
                  <CardTitle className="text-lg leading-none">
                    Slack Channel Configuration
                  </CardTitle>
                  <CardDescription>
                    Map each search to a specific Slack channel.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  {((isHubspotBeforeSlack && isHubspotExisting) ||
                    (isSalesforceBeforeSlack && isSalesforceExisting)) && (
                    <>
                      <FormField
                        control={form.control}
                        name={`slackOwnerNotification`}
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <div className="space-y-1.5">
                              <FormLabel>
                                Would you like to send a notification to the
                                company&apos;s owner when possible?
                              </FormLabel>
                              <p className="text-[13px] text-slate-500">
                                When not possible, the data will be sent to the
                                channel specified below.
                              </p>
                            </div>
                            <FormControl>
                              <RadioGroup
                                onValueChange={(value) => {
                                  field.onChange(value as "yes" | "no")
                                }}
                                defaultValue={
                                  ownerNotificationYes ? "yes" : "no"
                                }
                                className="flex flex-col space-y-1"
                              >
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="yes" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    Yes
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="no" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    No
                                  </FormLabel>
                                </FormItem>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Separator className="my-7" />
                    </>
                  )}

                  {formValues.slackMappings?.map((_, i) => (
                    <div
                      key={i}
                      className="mb-4 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)] items-center gap-3"
                    >
                      <div>
                        <FormField
                          control={form.control}
                          name={`slackMappings.${i}.searchName`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <input
                                  type="text"
                                  {...field}
                                  disabled
                                  className="border-input w-full rounded-md border bg-gray-100 px-3 py-2 text-sm"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="h-full">
                        <div className="flex h-10 items-center">
                          <Icons.arrowRight className="h-[20px] w-[20px] text-sm text-slate-300" />
                        </div>
                      </div>
                      <div>
                        <FormField
                          control={form.control}
                          name={`slackMappings.${i}.slackChannel`}
                          render={({ field }) => (
                            <FormItem>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant="outline"
                                      role="combobox"
                                      className={cn(
                                        "w-full justify-between",
                                        !field.value && "text-muted-foreground"
                                      )}
                                    >
                                      {field.value
                                        ? channels.channels.find(
                                            (channel) =>
                                              channel.value === field.value
                                          )?.label
                                        : "Select channel"}
                                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-[300px] p-0">
                                  <Command>
                                    <CommandInput placeholder="Search channel..." />
                                    <CommandEmpty>
                                      No channel found.
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {channels.channels.map((channel) => (
                                        <CommandItem
                                          value={channel.label}
                                          key={channel.value}
                                          onSelect={() => {
                                            form.setValue(
                                              `slackMappings.${i}.slackChannel`,
                                              channel.value
                                            )
                                          }}
                                        >
                                          <Check
                                            className={cn(
                                              "mr-2 h-4 w-4",
                                              channel.value === field.value
                                                ? "opacity-100"
                                                : "opacity-0"
                                            )}
                                          />
                                          {channel.label}
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  </Command>
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  ))}
                </CardContent>
                <CardFooter className="p-0">
                  <Button type="submit">
                    {isSaving ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    <span>Save step</span>
                  </Button>
                </CardFooter>
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">
              Slack Notifications
            </CardTitle>
            <CardDescription>
              Map each search to a specific Slack channel.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="slack" setSteps={setSteps} />
      )}
    </Card>
  )
}
