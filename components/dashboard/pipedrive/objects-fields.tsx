"use client"

import { useEffect } from "react"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/logos/pipedrive"
import DatachimpLogo from "@/components/logos/datachimp"
import MappingPair from "@/components/dashboard/pipedrive/mapping-pair"
import { Button } from "@/components/ui/button"
import { FormLabel } from "@/components/react-hook-form/form"

interface ObjectsFieldsProps {
  form: any
  path: string
  title: string
  formKey: string
  formValues: any
  description: string
  properties: any
  enrichmentProviders?: any
}

export default function ObjectsFields({
  form,
  path,
  title,
  formKey,
  formValues,
  description,
  properties,
  enrichmentProviders,
}: ObjectsFieldsProps) {
  useEffect(() => {
    return () =>
      form.setValue(formKey, [
        {
          datachimp: { label: "", value: "" },
          pipedrive: { label: "", value: "" },
        },
      ])
  }, [])

  console.log("formKey >>>", formKey)
  console.log("formValues[formKey] >>>", formValues[formKey])

  const elements = formValues[formKey].map((mappingField, index) => (
    <MappingPair
      key={index}
      form={form}
      path={path}
      index={index}
      formKey={formKey}
      formValues={formValues}
      properties={properties}
      mappingField={mappingField}
      enrichmentProviders={enrichmentProviders}
    />
  ))

  return (
    <div className="w-[600px] text-[13px]">
      <div className="mb-8 space-y-1.5">
        <FormLabel>{title}</FormLabel>
        <p className="w-4/5 text-slate-500">{description}</p>
      </div>
      <div className="mb-2 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)_24px] gap-3">
        <div className="flex">
          <div className="mr-2 h-4 w-4">
            <DatachimpLogo />
          </div>
          <FormLabel>Datachimp</FormLabel>
        </div>
        <div />
        <div className="flex">
          <div className="mr-2 h-4 w-4">
            <PipedriveLogo />
          </div>
          <FormLabel>Pipedrive</FormLabel>
        </div>
      </div>
      <div className="mb-6">{elements}</div>
      <Button
        type="button"
        variant="outline"
        onClick={() =>
          form.setValue(formKey, [
            ...formValues[formKey],
            {
              datachimp: { label: undefined, value: undefined },
              pipedrive: { label: undefined, value: undefined },
            },
          ])
        }
      >
        Add mapping fields
      </Button>
    </div>
  )
}
