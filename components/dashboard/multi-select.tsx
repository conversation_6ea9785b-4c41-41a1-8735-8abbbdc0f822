"use client"

import React, { forwardRef, ForwardRefRenderFunction } from "react"
import { ControllerRenderProps, FieldValues } from "react-hook-form"

import { cn } from "@/lib/utils"

import { Icons } from "@/components/icons"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

interface MultiSelectProps extends ControllerRenderProps<FieldValues, string> {
  item: string
  placeholder: string
  options: {
    label: string
    value: string | number | boolean
    excluded?: boolean
  }[]
  allowExclusion?: boolean
}

const MultiSelect: ForwardRefRenderFunction<
  HTMLInputElement,
  MultiSelectProps
> = ({ item, placeholder, value, options, onChange, allowExclusion }, ref) => {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (element) => {
    const newValue = value.some((item) => item.value === element.value)
      ? value.filter((item) => item.value !== element.value)
      : [...value, { ...element, excluded: false }]
    onChange(newValue)
  }

  const removeBadge = (index) => {
    onChange(value.filter((_, i) => i !== index))
  }

  const toggleExclusion = (index) => {
    onChange(
      value.map((entry, i) =>
        i === index ? { ...entry, excluded: !entry.excluded } : entry
      )
    )
  }

  return (
    <div ref={ref}>
      <div
        className={cn(
          "mb-2 flex w-full flex-wrap gap-2",
          !value.length && "hidden"
        )}
      >
        {value.map((item, index) => (
          <Badge
            key={index}
            variant="secondary"
            className={cn(
              "rounded-md text-sm font-normal",
              allowExclusion && item.excluded
                ? "bg-red-100 text-red-600 hover:bg-red-100/80"
                : "bg-green-100 text-green-600 hover:bg-green-100/80"
            )}
          >
            {allowExclusion && (
              <button
                type="button"
                className="focus:shadow-outline mr-1 rounded-full outline-none"
                onClick={() => toggleExclusion(index)}
              >
                {!item.excluded ? (
                  <Icons.exclude className="h-3 w-3" />
                ) : (
                  <Icons.include className="h-3 w-3" />
                )}
              </button>
            )}
            {item.label}
            <button
              type="button"
              className="ml-1 outline-none"
              onClick={() => removeBadge(index)}
            >
              <Icons.close className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between border-slate-300 font-normal text-slate-400 hover:bg-slate-50"
            )}
          >
            {placeholder}
            <Icons.chevronUpDown className="ml-2 h-4 w-4 shrink-0" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[550px] p-0" align="start">
          <Command className="flex max-h-[300px] flex-col overflow-hidden">
            <CommandInput
              placeholder={`Search ${item}...`}
              className="sticky top-0 z-10"
            />
            <CommandEmpty>No {item} found.</CommandEmpty>
            <CommandGroup className="overflow-auto">
              {options.map((element, i) => (
                <CommandItem
                  key={i}
                  onSelect={() => handleSelect(element)}
                  className="cursor-pointer hover:bg-slate-50"
                >
                  <Icons.check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value.some((item) => item.value === element.value)
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {element.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}

export default forwardRef(MultiSelect)
