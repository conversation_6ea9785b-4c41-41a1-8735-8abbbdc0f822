"use client"

import * as z from "zod"
import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { apiKeySchema } from "@/lib/validations/api-key"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Form } from "@/components/react-hook-form/form"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/react-hook-form/form"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card"

import ProspeoLogo from "@/components/logos/prospeo"
import ApolloLogo from "@/components/logos/apollo"
import LushaLogo from "@/components/logos/lusha"
import DropcontactLogo from "@/components/logos/dropcontact"

interface SecretValue {
  [key: string]: string | undefined
}

interface ApiKeyCardProps {
  integration: {
    title: string
    description: string
    logo: string
    connection: string
  }
  secrets: SecretValue[]
}

const logos = {
  prospeo: <ProspeoLogo />,
  apollo: <ApolloLogo />,
  lusha: <LushaLogo />,
  dropcontact: <DropcontactLogo />,
}

export default function ApiKeyCard({ integration, secrets }: ApiKeyCardProps) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isDisplayed, setIsDisplayed] = useState(false)
  const [doesKeyExist, setDoesKeyExist] = useState(false)

  const form = useForm<z.infer<typeof apiKeySchema>>({
    resolver: zodResolver(apiKeySchema),
    defaultValues: {
      key: "",
      api: integration.connection,
    },
  })

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: z.infer<typeof apiKeySchema>) {
    setIsSaving(true)

    const response = await fetch(`/api/settings/api-keys`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        key: data.key,
        api: data.api,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your API key was not saved. Please try again.",
      })
    }

    router.refresh()

    return toast({
      title: "Your API key has been saved 🎉",
      description: "You're now able to use this API in Datachimp workflows.",
    })
  }

  async function onDelete() {
    setIsDeleting(true)

    const response = await fetch(
      `/api/settings/api-keys?api=${formValues.api}`,
      {
        method: "DELETE",
      }
    )

    setIsDeleting(false)

    if (!response?.ok) {
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your API key was not deleted. Please try again.",
      })
    }

    router.refresh()
    form.reset({ api: "", key: "" })
    setDoesKeyExist(false)

    return toast({
      title: "Your API key has been deleted 🎉",
      description:
        "You won't be able to use this API in Datachimp workflows anymore.",
    })
  }

  //   Load API key in input on mount
  useEffect(() => {
    const api = secrets.find((secret) => secret[integration.connection])

    if (api) {
      const entries = Object.entries(api)
      if (entries.length > 0) {
        const [apiKey, keyValue] = entries[0]
        form.reset({ api: apiKey, key: keyValue })
        setDoesKeyExist(true)
      } else {
        form.reset({ api: "", key: "" })
      }
    }
  }, [secrets, integration, form])

  return (
    <Card>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader className="flex flex-row items-center space-x-2">
            <div className="h-7 w-7">{logos[integration?.logo]}</div>
            <div>
              <CardTitle>{integration.title}</CardTitle>
              <CardDescription>{integration.description}</CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            <FormField
              control={form.control}
              name="key"
              render={({ field }) => {
                return (
                  <FormItem className="space-y-3">
                    <div className="grid w-full items-center gap-4">
                      <div className="flex flex-col space-y-1.5">
                        <FormLabel>API key</FormLabel>
                        <div className="relative">
                          <FormControl>
                            <Input
                              type={isDisplayed ? "text" : "password"}
                              placeholder="Your API key"
                              {...field}
                            />
                          </FormControl>
                          {isDisplayed ? (
                            <Icons.eyeOff
                              className="absolute right-4 top-3 h-4 w-4 cursor-pointer text-slate-500"
                              onClick={() => setIsDisplayed(!isDisplayed)}
                            />
                          ) : (
                            <Icons.eye
                              className="absolute right-4 top-3 h-4 w-4 cursor-pointer text-slate-500"
                              onClick={() => setIsDisplayed(!isDisplayed)}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </FormItem>
                )
              }}
            />
          </CardContent>
          <CardFooter>
            {doesKeyExist ? (
              <Button
                type="button"
                variant="outline"
                className="text-red-500"
                onClick={() => onDelete()}
              >
                {isDeleting ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                <span>Remove</span>
              </Button>
            ) : (
              <Button type="submit">
                {isSaving ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                <span>Save</span>
              </Button>
            )}
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
