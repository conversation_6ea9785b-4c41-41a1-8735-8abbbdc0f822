"use client"

import { User } from "@prisma/client"
import Nan<PERSON> from "@nangohq/frontend"
import { Icons } from "@/components/icons"
import { useRouter } from "next/navigation"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import Slack<PERSON>ogo from "@/components/logos/slack"
import <PERSON><PERSON><PERSON> from "@/components/logos/teams"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/logos/hubspot"
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/logos/pipedrive"
import SalesforceLogo from "@/components/logos/salesforce"

import { cn } from "@/lib/utils"

interface IntegrationCardProps {
  connected: boolean
  integration: {
    title: string
    description: string
    logo: string
    connection: string
  }
  user: Pick<User, "clientId">
}

const nango = new Nango({ publicKey: "f69d4d4b-5a78-4ded-9d13-d5eec61ffa5e" })

const logos = {
  slack: <SlackLogo />,
  teams: <TeamsLogo />,
  hubspot: <HubspotLogo />,
  pipedrive: <PipedriveLogo />,
  salesforce: <SalesforceLogo />,
}

export default function IntegrationCard({
  connected,
  integration,
  user,
}: IntegrationCardProps) {
  const router = useRouter()

  async function handleNangoConnection() {
    try {
      await nango.auth(integration.connection, user.clientId)
      router.refresh()
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="h-7 w-7">{logos[integration?.logo]}</div>
          <div>
            <CardTitle>{integration.title}</CardTitle>
            <CardDescription>{integration.description}</CardDescription>
          </div>
        </div>
        {connected ? (
          <Icons.check2 className={cn("h-5 w-5 text-green-600")} />
        ) : (
          <Icons.sync
            className={cn("h-5 w-5 cursor-pointer opacity-50")}
            onClick={handleNangoConnection}
          />
        )}
      </CardHeader>
    </Card>
  )
}
