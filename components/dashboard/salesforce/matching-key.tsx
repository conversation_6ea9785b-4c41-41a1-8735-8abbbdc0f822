"use client"

import { useEffect, useState } from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { Icons } from "@/components/icons"
import SalesforceLogo from "@/components/logos/salesforce"
import DatachimpLogo from "@/components/logos/datachimp"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import { cn } from "@/lib/utils"

const datachimpMatchingKeys = {
  datachimp: [
    { label: "Company name", value: "company_name" },
    { label: "Company Linkedin id", value: "company_linkedin_id" },
    { label: "Company Linkedin url", value: "linkedin_company_url" },
    { label: "Domain", value: "domain" },
  ],
}

export default function MatchingKey({ form }) {
  const [salesforceProperties, setSalesforceProperties] = useState<any>()
  const [open, setOpen] = useState({
    datachimp: false,
    salesforce: false,
  })

  // Fetch company object properties
  useEffect(() => {
    const fetchData = async () => {
      try {
        const queryParams = new URLSearchParams({
          object: "Account",
        })

        const response = await fetch(
          `/api/salesforce/properties?${queryParams}`
        )

        if (!response.ok) {
          throw new Error("Error fetching data")
        }

        const jsonData = await response.json()
        setSalesforceProperties(jsonData.properties)
      } catch (error) {
        console.error(error)
      }
    }

    fetchData()

    return () => {
      form.setValue("datachimp_salesforce_matching_key", {
        datachimp: {
          label: "",
          value: "",
        },
        salesforce: {
          label: "",
          value: "",
        },
      })
    }
  }, [])

  return (
    <div className="w-[600px] text-[13px]">
      <div className="mb-8 space-y-1.5">
        <FormLabel>
          Which key will be used to match Datachimp results to Salesforce
          companies records?
        </FormLabel>
        <p className="w-4/5 text-slate-500">
          For example, it can be the company domain or company linkedin id.
        </p>
      </div>
      <div className="mb-2 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)_24px] gap-3">
        <div className="flex">
          <div className="mr-2 h-4 w-4">
            <DatachimpLogo />
          </div>
          <FormLabel>Datachimp</FormLabel>
        </div>
        <div />
        <div className="flex">
          <div className="mr-2 h-4 w-4">
            <SalesforceLogo />
          </div>
          <FormLabel>Salesforce</FormLabel>
        </div>
      </div>
      <div className="grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)_24px] items-center gap-3">
        <div>
          <FormField
            control={form.control}
            name="datachimp_salesforce_matching_key.datachimp"
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col space-y-2">
                  <Popover
                    open={open.datachimp}
                    onOpenChange={() =>
                      setOpen({ ...open, datachimp: !open.datachimp })
                    }
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={open.datachimp}
                          className={cn(
                            "justify-between truncate",
                            !field.value.value && "text-slate-500"
                          )}
                        >
                          {field.value.value
                            ? datachimpMatchingKeys.datachimp.find(
                                (object) => object.value === field.value?.value
                              )?.label
                            : "Select a key..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[260px] bg-white p-0">
                      <Command>
                        <CommandInput placeholder="Search matching key..." />
                        <CommandEmpty>No matching found.</CommandEmpty>
                        <CommandGroup
                          className="overflow-scroll"
                          style={{
                            height:
                              32 * datachimpMatchingKeys?.datachimp.length +
                              8 +
                              "px",
                            maxHeight: "15rem",
                          }}
                        >
                          {datachimpMatchingKeys?.datachimp.map((object) => (
                            <CommandItem
                              key={object.value}
                              onSelect={(value) => {
                                const matchingKey =
                                  datachimpMatchingKeys.datachimp.find(
                                    (c) => c.label.toLowerCase() === value
                                  )
                                form.setValue(
                                  "datachimp_salesforce_matching_key.datachimp",
                                  matchingKey
                                )
                              }}
                              className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  object.value === field.value?.value
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {object.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </div>
        <div className="h-full">
          <div className="flex h-10 items-center">
            <Icons.arrowRight className="h-[20px] w-[20px] text-sm text-slate-300" />
          </div>
        </div>
        <div>
          <FormField
            control={form.control}
            name="datachimp_salesforce_matching_key.salesforce"
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col space-y-2">
                  <Popover
                    open={open.salesforce}
                    onOpenChange={() =>
                      setOpen({ ...open, salesforce: !open.salesforce })
                    }
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={open.salesforce}
                          className={cn(
                            "justify-between truncate",
                            !field.value.value && "text-slate-500"
                          )}
                        >
                          {field.value.value
                            ? salesforceProperties?.find(
                                (object) => object.name === field.value?.value
                              )?.label
                            : "Select a key..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[260px] bg-white p-0">
                      <Command>
                        <CommandInput placeholder="Search matching key..." />
                        <CommandEmpty>No matching found.</CommandEmpty>
                        <CommandGroup
                          className="overflow-scroll"
                          style={{
                            height:
                              32 * salesforceProperties?.length + 8 + "px",
                            maxHeight: "15rem",
                          }}
                        >
                          {salesforceProperties?.map((object) => (
                            <CommandItem
                              key={object.value}
                              onSelect={(value) => {
                                const matchingKey = salesforceProperties.find(
                                  (c) => c.label.toLowerCase() === value
                                )
                                form.setValue(
                                  "datachimp_salesforce_matching_key.salesforce",
                                  {
                                    label: matchingKey.label,
                                    value: matchingKey.name,
                                  }
                                )
                              }}
                              className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  object.name === field.value?.value
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {object.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage className="text-[13px]" />
                </FormItem>
              )
            }}
          />
        </div>
      </div>
    </div>
  )
}
