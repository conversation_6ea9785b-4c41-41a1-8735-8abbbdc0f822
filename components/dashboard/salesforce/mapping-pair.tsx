"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Check, ChevronsUpDown } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/text-area"
import datachimpNewHiresMappingKeys from "@/lib/new-hires-mapping-pairs.json"
import datachimpJobOffersMappingKeys from "@/lib/job-offers-mapping-pairs.json"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import { cn } from "@/lib/utils"

const staticIcons = {
  string: <Icons.text className="mr-1 h-4 w-4 shrink-0" />,
  number: <Icons.hash className="mr-1 h-4 w-4 shrink-0" />,
  boolean: <Icons.boolean className="mr-1 h-4 w-4 shrink-0" />,
}

export default function MappingPair({
  form,
  path,
  index,
  formKey,
  formValues,
  properties,
  mappingField,
  enrichmentProviders,
}) {
  const [staticInput, setStaticInput] = useState("string")
  const [staticString, setStaticString] = useState<string>("")
  const [staticNumber, setStaticNumber] = useState<number>()
  const [staticBoolean, setStaticBoolean] = useState<string>("false")
  const [dynamicTemplate, setDynamicTemplate] = useState<any>("")
  const [open, setOpen] = useState<any>({ datachimp: false, salesforce: false })

  console.log("mapping field >>>", mappingField)

  function getMappingKeys(path) {
    switch (path) {
      case "new-hires":
        return datachimpNewHiresMappingKeys
      case "job-changes":
        return datachimpNewHiresMappingKeys
      case "job-offers":
        if (
          enrichmentProviders &&
          typeof enrichmentProviders === "object" &&
          "bettercontact" in enrichmentProviders
        ) {
          return [
            ...datachimpJobOffersMappingKeys,
            ...datachimpNewHiresMappingKeys,
          ].filter(
            (item, index, array) =>
              array.findIndex(
                (t) => t.label === item.label && t.value === item.value
              ) === index
          )
        } else {
          return datachimpJobOffersMappingKeys
        }
      default:
        return []
    }
  }

  const providerKeys = {
    apollo: ["email", "email_status"],
    prospeo: ["email", "email_status"],
    dropcontact: [
      "civility",
      "email",
      "naf5_code",
      "naf5_des",
      "phone",
      "siren",
      "siret",
      "siret_address",
      "siret_city",
      "siret_zip",
      "vat",
    ],
    bettercontact: ["email", "email_status"],
  }

  // Add enrichment providers properties to Datachimp mapping
  function getEnrichmentMapping() {
    let formattedArray: any = []

    for (let [provider, value] of Object.entries(enrichmentProviders)) {
      if (value && provider in providerKeys) {
        for (let key of providerKeys[provider as keyof typeof providerKeys]) {
          formattedArray.push({
            value: `${provider}_${key}`,
            label: formatLabel(provider, key),
          })
        }
      }
    }

    return formattedArray
  }

  function formatLabel(provider: string, key: string): string {
    const capitalizedProvider = capitalizeFirstLetter(provider)
    const formattedKey = key.split("_").map(capitalizeFirstLetter).join(" ")
    return `${capitalizedProvider} ${formattedKey}`
  }

  function capitalizeFirstLetter(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1)
  }

  let datachimpMappingKeys = getMappingKeys(path)

  if (enrichmentProviders) {
    datachimpMappingKeys = datachimpMappingKeys = [
      ...datachimpMappingKeys,
      ...getEnrichmentMapping(),
    ]
  }

  return (
    <div className="mb-4 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)_24px] items-start gap-3">
      <div>
        <FormField
          control={form.control}
          name={`${formKey}[${index}].datachimp`}
          render={({ field }) => {
            return (
              <FormItem className="flex flex-col space-y-2">
                <Popover
                  open={open.datachimp}
                  onOpenChange={() =>
                    setOpen({ ...open, datachimp: !open.datachimp })
                  }
                >
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open.datachimp}
                        className={cn(
                          "justify-between truncate",
                          !mappingField.datachimp.value && "text-slate-500"
                        )}
                      >
                        {
                          {
                            static: (
                              <>
                                <span className="flex w-[200px] items-center justify-start truncate rounded-md border border-blue-500 bg-blue-50 px-2 py-1 font-normal text-blue-500">
                                  {
                                    staticIcons[
                                      mappingField.datachimp.staticValueType
                                    ]
                                  }
                                  {mappingField.datachimp?.value?.toString()}
                                </span>
                              </>
                            ),
                            dynamic: (
                              <>
                                <span className="flex w-[200px] items-center justify-start truncate rounded-md border border-blue-500 bg-blue-50 px-2 py-1 font-normal text-blue-500">
                                  {
                                    <Icons.braces className="mr-1 h-4 w-4 shrink-0" />
                                  }
                                  {mappingField.datachimp.value}
                                </span>
                              </>
                            ),
                            standard: (
                              <>
                                {mappingField.datachimp.value
                                  ? mappingField.datachimp.label
                                  : "Select a key..."}
                              </>
                            ),
                          }[mappingField.datachimp.type]
                        }

                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[600px] bg-white p-0">
                    <Command className="p-4">
                      <Tabs
                        defaultValue="column"
                        className="w-full"
                        onValueChange={() => {
                          setStaticInput("string")
                        }}
                      >
                        <TabsList>
                          <TabsTrigger value="column">Column</TabsTrigger>
                          <TabsTrigger value="static">Static value</TabsTrigger>
                          <TabsTrigger value="dynamic">
                            Dynamic value
                          </TabsTrigger>
                        </TabsList>
                        <TabsContent value="column" className="border-none p-0">
                          <div className="rounded-md border">
                            <CommandInput
                              placeholder="Search matching key..."
                              className="border-b-0"
                            />
                          </div>
                          <CommandEmpty>No matching found.</CommandEmpty>
                          <CommandGroup
                            className="overflow-scroll"
                            style={{
                              height:
                                32 * datachimpMappingKeys?.length + 8 + "px",
                              maxHeight: "15rem",
                            }}
                          >
                            {datachimpMappingKeys?.map((property) => (
                              <CommandItem
                                key={property.value}
                                onSelect={(value) => {
                                  const matchingKey = datachimpMappingKeys.find(
                                    (c) => c.label.toLowerCase() === value
                                  )
                                  form.setValue(
                                    `${formKey}[${index}].datachimp`,
                                    { ...matchingKey, type: "standard" }
                                  )
                                }}
                                className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    property.value ===
                                      mappingField.datachimp?.value
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                                {property.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </TabsContent>
                        <TabsContent value="static" className="border-none p-0">
                          <div className="flex">
                            <RadioGroup
                              onValueChange={setStaticInput}
                              defaultValue="string"
                              className="flex w-1/3 flex-col space-y-1 border-r pb-10"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="string" id="r1" />
                                <Label className="font-normal" htmlFor="r1">
                                  String
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="number" id="r2" />
                                <Label className="font-normal" htmlFor="r2">
                                  Number
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="boolean" id="r3" />
                                <Label className="font-normal" htmlFor="r3">
                                  Boolean
                                </Label>
                              </div>
                            </RadioGroup>
                            <div className="flex w-2/3 flex-col justify-between pl-4">
                              {
                                {
                                  string: (
                                    <>
                                      <Input
                                        placeholder="Enter your string"
                                        defaultValue={
                                          (mappingField.datachimp.type ===
                                            "static" &&
                                            mappingField.datachimp.value) ||
                                          staticString
                                        }
                                        onChange={(e) =>
                                          setStaticString(e.target.value)
                                        }
                                      />
                                      <div className="flex justify-end">
                                        <Button
                                          onClick={() => {
                                            form.setValue(
                                              `${formKey}[${index}].datachimp`,
                                              {
                                                label: staticString,
                                                value: staticString,
                                                type: "static",
                                                staticValue: staticString,
                                                staticValueType: "string",
                                              }
                                            )
                                            setOpen({
                                              ...open,
                                              datachimp: !open.datachimp,
                                            })
                                          }}
                                        >
                                          Save
                                        </Button>
                                      </div>
                                    </>
                                  ),
                                  number: (
                                    <>
                                      <Input
                                        placeholder="Enter your number"
                                        type="number"
                                        defaultValue={
                                          mappingField.datachimp.value ||
                                          staticNumber
                                        }
                                        onChange={(e) =>
                                          setStaticNumber(
                                            Number(e.target.value)
                                          )
                                        }
                                      />
                                      <div className="flex justify-end">
                                        <Button
                                          onClick={() => {
                                            form.setValue(
                                              `${formKey}[${index}].datachimp`,
                                              {
                                                label: staticNumber,
                                                value: staticNumber,
                                                type: "static",
                                                staticValue: staticNumber,
                                                staticValueType: "number",
                                              }
                                            )
                                            setOpen({
                                              ...open,
                                              datachimp: !open.datachimp,
                                            })
                                          }}
                                        >
                                          Save
                                        </Button>
                                      </div>
                                    </>
                                  ),
                                  boolean: (
                                    <>
                                      <RadioGroup
                                        onValueChange={(value) =>
                                          setStaticBoolean(value)
                                        }
                                        defaultValue={
                                          staticBoolean ||
                                          mappingField.datachimp.value
                                        }
                                        className="space-y-1"
                                      >
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem
                                            value="true"
                                            id="r4"
                                          />
                                          <Label
                                            className="font-normal"
                                            htmlFor="r4"
                                          >
                                            True
                                          </Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem
                                            value="false"
                                            id="r5"
                                          />
                                          <Label
                                            className="font-normal"
                                            htmlFor="r5"
                                          >
                                            False
                                          </Label>
                                        </div>
                                      </RadioGroup>
                                      <div className="flex justify-end">
                                        <Button
                                          onClick={() => {
                                            form.setValue(
                                              `${formKey}[${index}].datachimp`,
                                              {
                                                label: staticBoolean,
                                                value: staticBoolean,
                                                type: "static",
                                                staticValue: staticBoolean,
                                                staticValueType: "boolean",
                                              }
                                            )
                                            setOpen({
                                              ...open,
                                              datachimp: !open.datachimp,
                                            })
                                          }}
                                        >
                                          Save
                                        </Button>
                                      </div>
                                    </>
                                  ),
                                }[staticInput]
                              }
                            </div>
                          </div>
                        </TabsContent>
                        <TabsContent
                          value="dynamic"
                          className="border-none p-0"
                        >
                          <div className="space-y-3">
                            <div className="flex h-[210px] space-x-2">
                              <Textarea
                                className="w-3/5"
                                placeholder="Click on any variable on the right to inject into Liquid template..."
                                // Avoid having a static string displayed
                                value={dynamicTemplate}
                                onChange={(e) => {
                                  setDynamicTemplate(e.target.value)
                                }}
                              />
                              <Tabs
                                defaultValue="columns-dynamic"
                                className="w-2/5 overflow-hidden"
                              >
                                <TabsList>
                                  <TabsTrigger value="columns-dynamic">
                                    Columns
                                  </TabsTrigger>
                                  <TabsTrigger value="metadata">
                                    Metadata
                                  </TabsTrigger>
                                </TabsList>
                                <TabsContent
                                  value="columns-dynamic"
                                  className="h-full border-none p-0"
                                >
                                  <div className="flex h-full flex-col overflow-y-auto">
                                    {datachimpMappingKeys?.map((el, i) => (
                                      <div
                                        key={i}
                                        className="transform cursor-pointer border-b border-b-slate-100 p-2 transition-all ease-in hover:bg-slate-50"
                                        onClick={() =>
                                          setDynamicTemplate(
                                            (prev) =>
                                              `${prev}{{ ${`row['${el.value}']`} }}`
                                          )
                                        }
                                      >
                                        <span className="text-sm">
                                          {el.label}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </TabsContent>
                                <TabsContent
                                  value="metadata"
                                  className="h-full border-none p-0"
                                >
                                  <div className="flex h-full flex-col overflow-y-auto">
                                    <div
                                      className="transform cursor-pointer border-b border-b-slate-100 p-2 transition-all ease-in hover:bg-slate-50"
                                      onClick={() =>
                                        setDynamicTemplate(
                                          (prev) =>
                                            `${prev}{{ context['current_timestamp'] }}`
                                        )
                                      }
                                    >
                                      <span className="text-sm">
                                        Current timestamp
                                      </span>
                                    </div>
                                  </div>
                                </TabsContent>
                              </Tabs>
                            </div>

                            <div className="flex justify-end">
                              <Button
                                onClick={() => {
                                  form.setValue(
                                    `${formKey}[${index}].datachimp`,
                                    {
                                      label: dynamicTemplate,
                                      value: dynamicTemplate,
                                      type: "dynamic",
                                    }
                                  )
                                  setOpen({
                                    ...open,
                                    datachimp: !open.datachimp,
                                  })
                                }}
                              >
                                Save
                              </Button>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )
          }}
        />
      </div>
      <div className="h-full">
        <div className="flex h-10 items-center">
          <Icons.arrowRight className="h-[20px] w-[20px] text-sm text-slate-300" />
        </div>
      </div>
      <div>
        <FormField
          control={form.control}
          name={`${formKey}[${index}].salesforce`}
          render={({ field }) => {
            return (
              <FormItem className="flex flex-col space-y-2">
                <Popover
                  open={open.salesforce}
                  onOpenChange={() =>
                    setOpen({ ...open, salesforce: !open.salesforce })
                  }
                >
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open.salesforce}
                        className={cn(
                          "justify-between truncate",
                          !mappingField.salesforce.value && "text-slate-500"
                        )}
                      >
                        {mappingField.salesforce.value
                          ? mappingField.salesforce.label
                          : "Select a key..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[260px] bg-white p-0">
                    <Command>
                      <CommandInput placeholder="Search matching key..." />
                      <CommandEmpty>No matching found.</CommandEmpty>
                      <CommandGroup
                        className="overflow-scroll"
                        style={{
                          height: 32 * properties?.length + 8 + "px",
                          maxHeight: "15rem",
                        }}
                      >
                        {properties?.map((property) => (
                          <CommandItem
                            key={property.name}
                            onSelect={(value) => {
                              let matchingKey = properties.find(
                                (c) => c.label.toLowerCase() === value
                              )
                              matchingKey = {
                                label: matchingKey.label,
                                value: matchingKey.name,
                              }
                              form.setValue(`${formKey}[${index}].salesforce`, {
                                ...matchingKey,
                                type: "standard",
                              })
                            }}
                            className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                property.name === mappingField.salesforce?.value
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            {property.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage className="text-[13px]" />
              </FormItem>
            )
          }}
        />
      </div>
      {formValues[formKey].length > 1 ? (
        <Button
          type="button"
          variant="ghost"
          className="p-0 opacity-50"
          aria-label="Remove mapping fields"
          onClick={() => {
            form.setValue(
              formKey,
              formValues[formKey].filter((_, i) => i !== index)
            )
          }}
        >
          &#10005;
        </Button>
      ) : null}
    </div>
  )
}
