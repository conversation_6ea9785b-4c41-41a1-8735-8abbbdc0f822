"use client"

import { useState, useEffect } from "react"

import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

import { Input } from "@/components/ui/input"
import { Icons } from "@/components/icons"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"

import { cn } from "@/lib/utils"

export default function ObjectCheck({
  form,
  formValues,
  objectSynced,
  setObjectSynced,
  setProperties,
}) {
  const router = useRouter()
  const { toast } = useToast()

  const [objectCheck, setObjectCheck] = useState(false)

  // Get customObjectProperties if custom config loaded from db and clean up custom properties when component unmounts
  useEffect(() => {
    if (formValues.salesforce_object_name) {
      testObjectConnection(formValues.salesforce_object_name, true)
    }
    return () => {
      setProperties(undefined)
      setObjectSynced(false)
      form.setValue("salesforce_object_name", "")
    }
  }, [])

  async function testObjectConnection(objectName, loadedFromDatabase = false) {
    setObjectCheck(true)

    const response = await fetch(`/api/salesforce/objects`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        customObject: objectName,
      }),
    })

    setObjectCheck(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    const result = await response.json()
    setObjectSynced(true)
    form.setValue("salesforce_object_name", objectName)
    setProperties(result)

    router.refresh()

    // Don't display toast if properties have been fetched after loading settings from db.
    if (!loadedFromDatabase) {
      return toast({
        title: "Success!",
        description:
          "The connection with your custom object has been established 🏄",
      })
    }
  }

  return (
    <FormField
      control={form.control}
      name="salesforce_object_name"
      render={({ field }) => {
        return (
          <FormItem>
            <FormLabel>
              Enter Salesforce&apos;s internal name of your custom object
            </FormLabel>
            <FormControl>
              <div className="flex items-center">
                <Input
                  placeholder="your custom object"
                  className="mr-4 w-[260px]"
                  onChange={(e) => {
                    form.setValue("salesforce_object_name", e.target.value)

                    if (objectSynced) {
                      setObjectSynced(false)
                    }
                  }}
                  defaultValue={formValues.salesforce_object_name}
                />
                {objectSynced ? (
                  <Icons.check2 className="h-5 w-5 text-green-600" />
                ) : (
                  <Icons.sync
                    className={cn(
                      "h-5 w-5 cursor-pointer opacity-50",
                      objectCheck ? "animate-spin" : ""
                    )}
                    onClick={() => testObjectConnection(field.value)}
                  />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
