"use client"

import Link from "next/link"
import { dashboardHeaderItem } from "types"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

interface DashboardHeaderProps {
  items: dashboardHeaderItem[]
  path: string
  children?: React.ReactNode
}

export function DashboardHeader({
  items,
  path,
  children,
}: DashboardHeaderProps) {
  const pathname = usePathname()
  const lastSegment = pathname?.substring(pathname.lastIndexOf("/") + 1)
  const segments = pathname?.split("/")
  const beforeLastSegment = segments && segments[segments.length - 2]

  if (!items?.length) {
    return null
  }

  return (
    <div className="mb-12 border-b border-b-slate-200">
      <div className="-mb-px flex text-sm">
        {items.map((item, index) => (
          <Link
            key={index}
            href={`${path}/${item.path}`}
            className={cn(
              "block whitespace-nowrap border-b px-4 pb-4 pt-2 transition duration-200 ease-in-out",
              lastSegment === item.path || beforeLastSegment == item.path
                ? "border-blue-600 font-semibold"
                : "hover:border-b-slate-400"
            )}
          >
            {item.title}
          </Link>
        ))}
      </div>
      {children}
    </div>
  )
}
