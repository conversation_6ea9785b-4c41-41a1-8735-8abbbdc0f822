"use client"

import React, { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

import { Icons } from "@/components/icons"
import { Switch } from "@/components/ui/switch"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import FirstStep from "@/components/dashboard/first-step"
import SlackCard from "@/components/dashboard/slack-card"
import TeamsCard from "@/components/dashboard/microsoft-card"
import HubspotCard from "@/components/dashboard/hubspot-card"
import PipedriveCard from "@/components/dashboard/pipedrive-card"
import WebhookCard from "@/components/dashboard/webhook-card"
import EnrichmentCard from "@/components/dashboard/enrichment-card"
import SalesforceCard from "@/components/dashboard/salesforce-card"
import <PERSON>lack<PERSON><PERSON> from "@/components/logos/slack"
import <PERSON><PERSON><PERSON> from "@/components/logos/teams"
import Hu<PERSON>pot<PERSON><PERSON> from "@/components/logos/hubspot"
import PipedriveLogo from "@/components/logos/pipedrive"
import SalesforceLogo from "@/components/logos/salesforce"

const integrations = [
  { type: "slack", key: 0 },
  { type: "microsoft-teams", key: 1 },
  { type: "hubspot", key: 2 },
  { type: "salesforce", key: 3 },
  { type: "pipedrive", key: 4 },
  { type: "enrichment", key: 5 },
  { type: "webhook", key: 6 },
]

export function Workflow({
  path,
  clientId,
  connections,
  workflowStatus,
  workflowSettings,
}) {
  const router = useRouter()
  const { toast } = useToast()

  const [enabled, setEnabled] = useState(
    workflowStatus === "enabled" ? true : false
  )
  const [isSaving, setIsSaving] = useState(false)
  const [steps, setSteps] = useState(
    workflowSettings || [{ type: "firstStep" }]
  )
  const [displayStepsAdd, setDisplayStepsAdd] = useState(true)

  async function onSubmit() {
    setIsSaving(true)

    const checkOpen = steps.some((step) => step.open)
    const isLastTypeEnrichment = steps[steps.length - 1].type === "enrichment"

    // Check if a step is still being edited
    if (checkOpen) {
      setIsSaving(false)

      return toast({
        title: "Please finish editing your steps 🚨",
        description: "At least one of your steps has not been saved.",
      })
    }

    // Check if the last step is the enrichment one
    if (isLastTypeEnrichment) {
      setIsSaving(false)

      return toast({
        title: "The final step should not be the enrichment process. 🚨",
        description:
          "Once the enrichment has been processed, the data needs to be sent somewhere.",
      })
    }

    const queryParams = new URLSearchParams({
      key: "workflow",
    })
    const response = await fetch(`/api/${path}/workflow?${queryParams}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data: steps,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    router.refresh()

    return toast({
      title: "Your workflow settings have been saved 🎉",
      description: "Your insights will be delivered directly to your tools.",
    })
  }

  function capitalizeWords(input: string) {
    return input
      .split("-")
      .map((word) => word[0].toLocaleUpperCase() + word.slice(1))
      .join("-")
  }

  // Compute whether the `+` icon should be displayed or not
  useEffect(() => {
    const integrationTypes = integrations.map((integration) => integration.type)
    const connectionsArr = connections.map((connection) => connection.provider)

    // Make sure you can't add twice same step
    for (let step of steps) {
      const index = integrationTypes.indexOf(step.type)
      if (index > -1) {
        integrationTypes.splice(index, 1)
      }
    }

    // Check if another step can be added based on existing integrations
    const otherIntegrationsToAdd = integrationTypes.some((el) =>
      connectionsArr.includes(el)
    )

    setDisplayStepsAdd(otherIntegrationsToAdd)
  }, [steps])

  return (
    <div className="flex gap-6">
      <div className="flex w-3/4 flex-col items-center">
        <div className="flex w-full flex-col items-center">
          {steps.map((step, i) => (
            <React.Fragment key={i}>
              <div className="flex w-full flex-col">
                {
                  {
                    firstStep: <FirstStep stepNumber={i} />,
                    enrichment: (
                      <EnrichmentCard
                        path={path}
                        step={step}
                        steps={steps}
                        stepNumber={i}
                        setSteps={setSteps}
                        connections={connections}
                      />
                    ),
                    webhook: (
                      <WebhookCard
                        path={path}
                        step={step}
                        stepNumber={i}
                        setSteps={setSteps}
                      />
                    ),
                    slack: (
                      <SlackCard
                        path={path}
                        step={step}
                        steps={steps}
                        stepNumber={i}
                        setSteps={setSteps}
                      />
                    ),
                    "microsoft-teams": (
                      <TeamsCard
                        path={path}
                        step={step}
                        steps={steps}
                        stepNumber={i}
                        setSteps={setSteps}
                      />
                    ),
                    hubspot: (
                      <HubspotCard
                        path={path}
                        step={step}
                        stepNumber={i}
                        setSteps={setSteps}
                        clientId={clientId}
                      />
                    ),
                    salesforce: (
                      <SalesforceCard
                        path={path}
                        step={step}
                        stepNumber={i}
                        setSteps={setSteps}
                      />
                    ),
                    pipedrive: (
                      <PipedriveCard
                        path={path}
                        step={step}
                        stepNumber={i}
                        setSteps={setSteps}
                      />
                    ),
                  }[step.type]
                }
              </div>
              {displayStepsAdd || steps.length - 1 !== i ? (
                <div className="h-6 border-l border-gray-200" />
              ) : null}
            </React.Fragment>
          ))}
        </div>
        <div className="mb-64">
          {displayStepsAdd ? (
            <DropdownMenu>
              <DropdownMenuTrigger className="cursor-pointer rounded-full border border-gray-200 p-1 focus:ring-2 focus:ring-brand-900 focus-visible:outline-none">
                <Icons.add className="text-gray-500" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56 bg-white">
                <DropdownMenuLabel>Step</DropdownMenuLabel>
                <DropdownMenuSeparator />

                {/* Only add a step for which the integration has been made + Don't create twice the same step */}
                {integrations
                  .filter(
                    (int) =>
                      connections.some((d) => d.provider === int.type) ||
                      ((clientId === "clxizm16y0000l40c0k4kjivg" ||
                        clientId === "clmeu2ril0000jv0fqp6own4e" ||
                        clientId === "cm1kja51x0000mn0cf3db8j28" ||
                        clientId === "cloint5sj0000l60fwbzj1p52") &&
                        int.type === "webhook")
                  )
                  .filter((int) => !steps.some((s) => s.type === int.type))
                  .map((integration) => (
                    <DropdownMenuItem
                      key={integration.key}
                      className="cursor-pointer py-2 hover:bg-slate-100"
                      onSelect={() =>
                        setSteps((curr) => [
                          ...curr,
                          { type: integration.type, open: true },
                        ])
                      }
                    >
                      <div className="mr-2 h-4 w-4">
                        {
                          {
                            enrichment: (
                              <Icons.enrichment className="h-4 w-4 text-slate-500" />
                            ),
                            webhook: (
                              <Icons.webhook className="h-4 w-4 text-slate-500" />
                            ),
                            slack: <SlackLogo />,
                            hubspot: <HubspotLogo />,
                            pipedrive: <PipedriveLogo />,
                            salesforce: <SalesforceLogo />,
                            "microsoft-teams": <TeamsLogo />,
                          }[integration.type]
                        }
                      </div>
                      {capitalizeWords(integration.type)}
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
          ) : null}
        </div>
      </div>
      <div className="sticky top-28 h-max w-1/4">
        <WorkflowOptions
          path={path}
          enabled={enabled}
          setEnabled={setEnabled}
          onSubmit={onSubmit}
          isSaving={isSaving}
        />
      </div>
    </div>
  )
}

export function WorkflowOptions({
  path,
  enabled,
  setEnabled,
  onSubmit,
  isSaving,
}) {
  const { toast } = useToast()

  async function updateWorkflow() {
    setEnabled(!enabled)

    const response = await fetch(`/api/${path}/workflow`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        update: enabled ? "pause" : "resume",
      }),
    })

    if (!response?.ok) {
      setEnabled(!enabled)

      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your workflow was not updated. Please try again.",
      })
    }
  }

  return (
    <>
      <div className="mb-12">
        <span className="text-sm font-medium leading-none text-slate-500">
          Workflow status
        </span>
        <Separator className="my-3" />
        <div className="flex items-center gap-3">
          <span className="w-16 text-xs font-medium uppercase leading-none text-slate-400">
            {!enabled ? "Disabled" : "Enabled"}
          </span>
          <Switch checked={enabled} onClick={updateWorkflow} />
        </div>
      </div>
      <div className="mb-20">
        <span className="text-sm font-medium leading-none text-slate-500">
          Workflow actions
        </span>
        <Separator className="my-3" />
        <div className="flex items-center gap-3">
          <Button
            type="submit"
            className="w-full focus:ring-0"
            onClick={onSubmit}
          >
            {isSaving ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            <span>Save workflow</span>
          </Button>
        </div>
      </div>
    </>
  )
}
