"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { computeFrequency } from "@/lib/utils"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormLabel,
} from "@/components/react-hook-form/form"
import { Form } from "@/components/react-hook-form/form"
import { Check, ChevronsUpDown } from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>rigger } from "@/components/ui/tabs"
import { cronTabs, cronOptions } from "@/config/cronConfig"
import { cronSchema } from "@/lib/validations/cron"
import { Icons } from "../icons"

import { cn } from "@/lib/utils"

interface SchedulerFormProps {
  path: string
}

export function SchedulerForm({ path }: SchedulerFormProps) {
  const { toast } = useToast()
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)
  const [open, setOpen] = useState(false)

  const timezones = (Intl as any).supportedValuesOf("timeZone")

  const form = useForm<z.infer<typeof cronSchema>>({
    resolver: zodResolver(cronSchema),
    defaultValues: {
      timeOfDay: "AM",
      minutes: 0,
    },
  })

  async function onSubmit(data: z.infer<typeof cronSchema>) {
    setIsSaving(true)

    const response = await fetch(`/api/${path}/cron`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        monthDay: data.monthDay,
        weekDay: data.weekDay,
        hours: data.hours,
        minutes: data.minutes,
        timeOfDay: data.timeOfDay,
        timezone: data.timezone,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your scheduler was not saved. Please try again.",
      })
    }

    router.refresh()

    return toast({
      title: "Your scheduler has been saved 🎉",
      description: computeFrequency(data),
    })
  }

  const { watch } = form
  const formValues = watch()

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <Tabs
            defaultValue="daily"
            className="w-full"
            onValueChange={() => form.reset()}
          >
            <TabsList>
              {cronTabs.map((tab, key) => (
                <TabsTrigger key={key} value={tab.value}>
                  {tab.content}
                </TabsTrigger>
              ))}
            </TabsList>
            {cronTabs.map((tab, key) => (
              <TabsContent key={key} value={tab.value}>
                {tab.value === "monthly" ? (
                  <div className="mb-6 grid w-full max-w-fit items-center gap-1.5">
                    <FormField
                      control={form.control}
                      name="monthDay"
                      render={({ field }) => {
                        return (
                          <FormItem className="space-y-3">
                            <FormLabel>Day of the month</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(parseInt(value))
                                  form.setValue("monthDay", parseInt(value))
                                }}
                              >
                                <FormControl>
                                  <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Select a day" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cronOptions.monthDays.map((monthDay) => (
                                    <SelectItem
                                      key={parseInt(monthDay.value)}
                                      value={monthDay.value}
                                    >
                                      {monthDay.content}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                ) : null}
                {tab.value === "weekly" ? (
                  <div className="mb-6 grid w-full max-w-fit items-center gap-1.5">
                    <FormField
                      control={form.control}
                      name="weekDay"
                      render={({ field }) => {
                        return (
                          <FormItem className="space-y-3">
                            <FormLabel>Day of the week</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(parseInt(value))
                                  form.setValue("weekDay", parseInt(value))
                                }}
                              >
                                <FormControl>
                                  <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Select a day" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cronOptions.weekDays.map((weekDay) => (
                                    <SelectItem
                                      key={parseInt(weekDay.value)}
                                      value={weekDay.value}
                                    >
                                      {weekDay.content}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                ) : null}
                <div className="mb-6 flex space-x-10">
                  <div className="grid w-full max-w-fit items-center gap-1.5">
                    <FormField
                      control={form.control}
                      name="hours"
                      render={({ field }) => {
                        return (
                          <FormItem className="space-y-3">
                            <FormLabel>Hours</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(parseInt(value))
                                  form.setValue("hours", parseInt(value))
                                }}
                              >
                                <FormControl>
                                  <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Select an hour" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cronOptions.hours.map((hour) => (
                                    <SelectItem
                                      key={parseInt(hour.value)}
                                      value={hour.value}
                                    >
                                      {hour.content}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                  <div className="grid w-full max-w-fit items-center gap-1.5">
                    <FormField
                      control={form.control}
                      name="timeOfDay"
                      render={({ field }) => {
                        return (
                          <FormItem className="space-y-3">
                            <FormLabel>Time of day</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value)
                                  form.setValue("timeOfDay", value)
                                }}
                                defaultValue="AM"
                                value={formValues.timeOfDay || "AM"}
                              >
                                <FormControl>
                                  <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Select time of day" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cronOptions.timesOfDay.map((monthDay) => (
                                    <SelectItem
                                      key={parseInt(monthDay.value)}
                                      value={monthDay.value}
                                    >
                                      {monthDay.content}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                </div>
                <div className="mb-6 grid w-full max-w-fit items-center space-y-3">
                  <FormLabel>Timezone</FormLabel>
                  <FormField
                    control={form.control}
                    name={"timezone"}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col space-y-2">
                          <Popover
                            open={open}
                            onOpenChange={() => setOpen(!open)}
                          >
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-[200px] justify-between truncate border-slate-300 px-3 text-sm font-normal"
                                >
                                  {formValues.timezone
                                    ? formValues.timezone
                                    : "Select a timezone"}
                                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-[200px] bg-white p-0">
                              <Command>
                                <CommandInput placeholder="Search matching key..." />
                                <CommandEmpty>No matching found.</CommandEmpty>
                                <CommandGroup
                                  className="overflow-scroll"
                                  style={{
                                    height: 32 * timezones?.length + 8 + "px",
                                    maxHeight: "15rem",
                                  }}
                                >
                                  {timezones?.map((object, i) => (
                                    <CommandItem
                                      key={i}
                                      onSelect={(value) => {
                                        form.setValue("timezone", object)
                                      }}
                                      className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          object === field.value
                                            ? "opacity-100"
                                            : "opacity-0"
                                        )}
                                      />
                                      {object}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>
                <Button type="submit">
                  {isSaving ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  <span>Save</span>
                </Button>
              </TabsContent>
            ))}
          </Tabs>
        </form>
      </Form>
    </div>
  )
}
