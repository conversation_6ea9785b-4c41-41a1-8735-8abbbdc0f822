"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { WebhookOperations } from "@/components/dashboard/webhook-operations"
import { Icons } from "@/components/icons"

import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"

interface WebhookItemProps {
  webhook: { url: string; status: string | null; path: string }
}

const getWebhookState = (status) => {
  if (status === "enabled") {
    return true
  }

  return false
}

export function WebhookItem({ webhook }: WebhookItemProps) {
  const { toast } = useToast()
  const [enabled, setEnabled] = useState(getWebhookState(webhook?.status))

  async function updateWebhook() {
    setEnabled(!enabled)

    const response = await fetch(`/api/${webhook.path}/webhook`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        update: enabled ? "pause" : "resume",
      }),
    })

    if (!response?.ok) {
      setEnabled(!enabled)

      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your scheduler was not updated. Please try again.",
      })
    }
  }

  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <span className="text-sm font-semibold">Webhook URL</span>
        <div className="flex items-center text-sm text-slate-600">
          <Icons.link className="mr-1 h-4 w-4" />
          <p>{webhook.url}</p>
        </div>
      </div>
      <div className="flex items-center gap-6">
        {enabled ? (
          <div className="inline-flex h-6 items-center justify-center rounded-md bg-green-100 px-[6px] py-0.5 text-xs font-normal text-green-600 ">
            Enabled
          </div>
        ) : (
          <div className="inline-flex h-6 items-center justify-center rounded-md bg-yellow-100 px-[6px] py-0.5 text-xs font-normal text-yellow-600 ">
            Disabled
          </div>
        )}
        <Switch checked={enabled} onClick={updateWebhook} />
        <WebhookOperations path={webhook.path} />
      </div>
    </div>
  )
}

WebhookItem.Skeleton = function SchedulerItemSkeleton() {
  return (
    <div className="p-4">
      <Skeleton className="h-5 w-2/5" />
    </div>
  )
}
