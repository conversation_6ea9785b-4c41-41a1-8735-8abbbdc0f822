"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"

import { Switch } from "@/components/ui/switch"
import { Skeleton } from "@/components/ui/skeleton"

interface WorkflowItemProps {
  workflow: { status: string; path: string }
}

const getWorkflowState = (status: string) => {
  if (status === "enabled") {
    return true
  }

  return false
}

export function WorkflowItem({ workflow }: WorkflowItemProps) {
  const { toast } = useToast()
  const [enabled, setEnabled] = useState(getWorkflowState(workflow.status))

  async function updateWorkflow() {
    setEnabled(!enabled)

    const response = await fetch(`/api/${workflow.path}/workflow`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        update: enabled ? "pause" : "resume",
      }),
    })

    if (!response?.ok) {
      setEnabled(!enabled)

      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: "Your scheduler was not updated. Please try again.",
      })
    }
  }

  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-2">
        <span className="text-sm font-medium">Workflow</span>
        <div className="flex items-center gap-x-1 rounded bg-slate-100 px-1.5 py-1 text-xs font-medium">
          <div className="h-3 w-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 133.2 133.2"
              width="100%"
              height="100%"
            >
              <path
                d="M27.2 80c0 7.3-5.9 13.2-13.2 13.2C6.7 93.2.8 87.3.8 80c0-7.3 5.9-13.2 13.2-13.2h13.2V80zm6.6 0c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2v33c0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V80z"
                fill="#E01E5A"
              />
              <path
                d="M47 27c-7.3 0-13.2-5.9-13.2-13.2C33.8 6.5 39.7.6 47 .6c7.3 0 13.2 5.9 13.2 13.2V27H47zm0 6.7c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H13.9C6.6 60.1.7 54.2.7 46.9c0-7.3 5.9-13.2 13.2-13.2H47z"
                fill="#36C5F0"
              />
              <path
                d="M99.9 46.9c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H99.9V46.9zm-6.6 0c0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V13.8C66.9 6.5 72.8.6 80.1.6c7.3 0 13.2 5.9 13.2 13.2v33.1z"
                fill="#2EB67D"
              />
              <path
                d="M80.1 99.8c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V99.8h13.2zm0-6.6c-7.3 0-13.2-5.9-13.2-13.2 0-7.3 5.9-13.2 13.2-13.2h33.1c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H80.1z"
                fill="#ECB22E"
              />
            </svg>
          </div>
          <p>Slack notifications</p>
        </div>
      </div>
      <div className="flex items-center gap-6">
        {enabled ? (
          <div className="inline-flex h-6 items-center justify-center rounded-md bg-green-100 px-[6px] py-0.5 text-xs font-normal text-green-600 ">
            Enabled
          </div>
        ) : (
          <div className="inline-flex h-6 items-center justify-center rounded-md bg-yellow-100 px-[6px] py-0.5 text-xs font-normal text-yellow-600 ">
            Disabled
          </div>
        )}
        <Switch checked={enabled} onClick={updateWorkflow} />
        {/* <WorkflowOperations path={workflow.path} /> */}
      </div>
    </div>
  )
}

WorkflowItem.Skeleton = function WorkflowItemSkeleton() {
  return (
    <div className="p-4">
      <Skeleton className="h-5 w-2/5" />
    </div>
  )
}
