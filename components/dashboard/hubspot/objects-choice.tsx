"use client"

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"

export default function ObjectsChoice({ form, formValues }) {
  const isCustomObject = formValues.hubspot_object_choice === "custom"
  const isStandardObject = formValues.hubspot_object_choice === "standard"

  return (
    <FormField
      control={form.control}
      name="hubspot_object_choice"
      render={({ field }) => {
        return (
          <FormItem className="space-y-3">
            <FormLabel>
              Which Hubspot object would you like to send data to?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={(value) => {
                  field.onChange(value)
                }}
                defaultValue={
                  isCustomObject ? "custom" : isStandardObject ? "standard" : ""
                }
                value={
                  isCustomObject ? "custom" : isStandardObject ? "standard" : ""
                }
                className="flex flex-col space-y-1"
              >
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="standard" />
                  </FormControl>
                  <FormLabel className="font-normal">
                    Companies object
                  </FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="custom" />
                  </FormControl>
                  <FormLabel className="font-normal">Custom object</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
