"use client"

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"

export default function CompanyCreationChoice({ form, formValues }) {
  const create = formValues.hubspot_company_creation === "yes"
  const dontCreate = formValues.hubspot_company_creation === "no"

  return (
    <FormField
      control={form.control}
      name="hubspot_company_creation"
      render={({ field }) => {
        return (
          <FormItem className="space-y-3">
            <FormLabel>
              Do you want to create the company in case it doesn&apos;t exist?
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={(value) => {
                  field.onChange(value)
                }}
                defaultValue={create ? "yes" : dontCreate ? "no" : ""}
                value={create ? "yes" : dontCreate ? "no" : ""}
                className="flex flex-col space-y-1"
              >
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="yes" />
                  </FormControl>
                  <FormLabel className="font-normal">Yes</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="no" />
                  </FormControl>
                  <FormLabel className="font-normal">No</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )
      }}
    />
  )
}
