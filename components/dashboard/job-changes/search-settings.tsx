"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import FileImport from "@/components/dashboard/job-changes/file-import"
import CrmImport from "@/components/dashboard/job-changes/crm-import"

export default function SearchSettings({ searchId, jobChangesConfig }) {
  const router = useRouter()

  return (
    <>
      <div className="flex justify-between">
        <div>
          <h2 className="text-md mb-1 font-semibold">Import</h2>
          <p className="mb-6 text-sm text-neutral-500">
            Import target accounts from your CRM or a CSV file to track job
            changes.
          </p>
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="secondary">╳</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Do you want to leave?</AlertDialogTitle>
              <AlertDialogDescription>
                Your search settings won&apos;t be saved if it&apos;s been
                edited.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => router.push("/dashboard/job-changes/track")}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      <Tabs
        defaultValue={
          jobChangesConfig?.search_settings?.crm ? "crmImport" : "csvImport"
        }
        className="w-full"
      >
        <TabsList className="mb-4">
          <TabsTrigger value="csvImport">CSV import</TabsTrigger>
          <TabsTrigger value="crmImport">CRM import</TabsTrigger>
        </TabsList>
        <TabsContent value="csvImport" className="border-none p-0">
          <FileImport searchId={searchId} jobChangesConfig={jobChangesConfig} />
        </TabsContent>
        <TabsContent value="crmImport" className="border-none p-0">
          <CrmImport searchId={searchId} jobChangesConfig={jobChangesConfig} />
        </TabsContent>
      </Tabs>
    </>
  )
}
