"use client"

import * as z from "zod"
import useSWR from "swr"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import Dropzone from "react-dropzone"
import { Label } from "@/components/ui/label"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/react-hook-form/form"

const FormSchema = z.object({
  file: z.any(),
  name: z.string().trim().nonempty({ message: "A name is required" }),
})

function detectDelimiter(line) {
  const commas = line.match(/,/g)?.length || 0
  const semicolons = line.match(/;/g)?.length || 0

  return commas > semicolons ? "," : ";"
}

function checkFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      const textStr = reader.result as string
      const lines = textStr.trim().split("\n")

      if (lines.length < 2) {
        reject("The file is empty.")
        return
      }

      const delimiter = detectDelimiter(lines[0])
      const headers = lines[0]
        .split(delimiter)
        .map((header) => header.trim().toLowerCase())
      const requiredColumns = ["company_name", "company_domain"]
      const optionalColumns = ["linkedin_company_id", "linkedin_company_url"]

      const missingColumns = requiredColumns.filter(
        (col) => !headers.includes(col)
      )

      const unexpectedColumns = headers.filter(
        (header) => ![...requiredColumns, ...optionalColumns].includes(header)
      )

      if (missingColumns.length > 0) {
        reject(
          `The CSV file is missing the following columns: ${missingColumns.join(
            ", "
          )}`
        )
      } else if (unexpectedColumns.length > 0) {
        reject(
          `The CSV file contains unexpected columns: ${unexpectedColumns.join(
            ", "
          )}`
        )
      } else {
        console.log("All required columns are present.")
        resolve(false)
      }
    }

    reader.readAsText(file)
  })
}

function getFileContent(file) {
  if (!file) {
    return null
  }

  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      resolve(reader.result as string)
    }

    reader.onerror = () => {
      reject(new Error("Error reading the file."))
    }

    reader.readAsText(file)
  })
}

export default function FileImport({ searchId, jobChangesConfig }) {
  const { toast } = useToast()
  const router = useRouter()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      file: null,
      name: "",
    },
  })
  const { watch } = form
  const formValues = watch()

  const [isSaving, setIsSaving] = useState(false)

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsSaving(true)

    if (data.file) {
      try {
        await checkFile(data.file)
      } catch (error) {
        setIsSaving(false)
        console.log(error)
        return toast({
          title: "CSV file error 🚨",
          description: error as string,
        })
      }
    }

    const fileContent = await getFileContent(data.file)

    const response = await fetch(`/api/job-changes/file-import`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data: fileContent,
        searchId: searchId,
        name: data.name,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    router.refresh()

    toast({
      title: "All good 🎉",
      description: "Your config have been saved!",
    })

    router.push("/dashboard/job-changes/track")
  }

  const fetcher = (url) => fetch(url).then((res) => res.text())
  const { data } = useSWR(
    `/api/job-changes/file-import?searchId=${searchId}`,
    fetcher
  )

  useEffect(() => {
    if (data && !data.includes("error")) {
      form.reset({
        file: { name: "accounts.csv" },
        name: jobChangesConfig?.name,
      })
    }
  }, [data, jobChangesConfig])

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="mb-32">
          <div className="mb-8 w-4/5">
            <div className="mb-1">
              <Label>Give a name to your config.</Label>
            </div>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input type="text" placeholder="Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <h2 className="text-md mb-1 font-semibold">CSV columns</h2>
          <p className="mb-6 text-sm text-neutral-500">
            company_name, company_domain, linkedin_company_url (optional),
            linkedin_company_id (optional)
          </p>
          <FormField
            control={form.control}
            name="file"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <>
                    <Dropzone
                      onDrop={(acceptedFiles) =>
                        form.setValue("file", acceptedFiles[0])
                      }
                      accept={{
                        "text/csv": [".csv"],
                      }}
                      disabled={!!formValues.file}
                    >
                      {({
                        getRootProps,
                        getInputProps,
                        isDragAccept,
                        isDragReject,
                      }) => (
                        <section>
                          <div
                            {...getRootProps()}
                            className={cn(
                              "flex h-64 transform flex-col items-center justify-center rounded-md border border-dashed bg-slate-50 transition-all ease-in-out",
                              isDragAccept && "border-blue-500 bg-blue-50",
                              isDragReject && "border-red-500 bg-red-50",
                              formValues.file
                                ? "cursor-no-drop"
                                : "cursor-pointer hover:bg-slate-100"
                            )}
                          >
                            <Input type="file" {...getInputProps()} />
                            {!formValues.file ? (
                              <>
                                <Icons.download className="mb-2 h-6 w-6 text-neutral-500" />
                                <p className="mb-6 text-sm text-neutral-500">
                                  Drag and drop or click to import your CSV
                                  file.
                                </p>
                              </>
                            ) : (
                              <>
                                <Icons.slash className="mb-2 h-6 w-6 text-neutral-500" />
                                <p className="mb-6 text-sm text-neutral-500">
                                  You can import 1 file max.
                                </p>
                              </>
                            )}
                          </div>
                        </section>
                      )}
                    </Dropzone>

                    {formValues.file ? (
                      <div className="inline-flex h-auto items-center rounded-md bg-blue-100 px-[6px] py-1 text-sm font-normal text-blue-600 focus:outline-none">
                        <Icons.page className="h-4 w-4" />
                        <div className="mx-1 text-justify">
                          {formValues.file.name}
                        </div>
                        <button
                          type="button"
                          onClick={() => form.setValue("file", null)}
                        >
                          &#10005;
                        </button>
                      </div>
                    ) : null}
                  </>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="mt-6">
            {isSaving ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            <span>Save</span>
          </Button>
        </form>
      </Form>
    </>
  )
}
