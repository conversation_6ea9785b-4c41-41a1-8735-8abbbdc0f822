"use client"

import * as z from "zod"
import use<PERSON><PERSON> from "swr"
import { useState } from "react"
import { cn, fetcher } from "@/lib/utils"
import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"

import { Check, ChevronsUpDown } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormItem,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  PopoverTrigger,
} from "@/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import ItemSkeleton from "@/components/dashboard/item-skeleton"
import HubspotLogo from "@/components/logos/hubspot"
import SalesforceLogo from "@/components/logos/salesforce"
import DatachimpLogo from "@/components/logos/datachimp"

const crmOptions = [
  { value: "salesforce", content: "Salesforce" },
  { value: "hubspot", content: "Hubspot" },
]

const datachimpPropertiesOptions = [
  { label: "Company Name", value: "company_name" },
  { label: "Company Domain", value: "company_domain" },
  { label: "Company Linkedin Url", value: "linkedin_company_url" },
  { label: "Company Linkedin Id", value: "linkedin_company_id" },
]

const FormSchema = z.object({
  crm: z.string(),
  name: z.string().trim().nonempty({ message: "A search name is required" }),
  list: z
    .object({ label: z.string(), value: z.string(), type: z.string() })
    .nullable(),
  change_type: z.enum(["from", "to", "both"]),
  properties_mapping: z.any(),
})

export default function CrmImport({ searchId, jobChangesConfig }) {
  const { toast } = useToast()
  const router = useRouter()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: jobChangesConfig?.search_settings || {
      change_type: "from",
    },
  })
  const { watch } = form
  const formValues = watch()

  // Fetch companies' properties depending on the CRM
  const { data: crmProperties, isLoading: crmLoading } = useSWR(
    formValues.crm &&
      `/api/${formValues.crm}/properties?object=${
        formValues.crm === "hubspot" ? "companies" : "Account"
      }`,
    fetcher
  )

  // Fetch lists depending on the CRM
  const { data: crmLists, isLoading: crmListsLoading } = useSWR(
    formValues.crm && `/api/${formValues.crm}/lists`,
    fetcher
  )

  const [isSaving, setIsSaving] = useState(false)
  const [open, setOpen] = useState<any>([
    { crm: false, datachimp: false },
    { crm: false, datachimp: false },
    { crm: false, datachimp: false },
    { crm: false, datachimp: false },
  ])
  const [openCrmLists, setOpenCrmLists] = useState(false)

  const isMappingValid = (formValues) => {
    let companyDomainPresent = false
    let companyNamePresent = false

    formValues.properties_mapping?.forEach((item) => {
      if (item.datachimp?.value === "company_domain" && item.crm?.value) {
        companyDomainPresent = true
      }
      if (item.datachimp?.value === "company_name" && item.crm?.value) {
        companyNamePresent = true
      }
    })

    return companyDomainPresent && companyNamePresent
  }

  const shouldDisplaySaveButton = isMappingValid(formValues)

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsSaving(true)

    if (!isMappingValid(data)) {
      setIsSaving(false)
      return toast({
        title: "CRM mapping error 🚨",
        description:
          "You need to map at least the company_name and company_domain keys.",
      })
    }

    const response = await fetch(`/api/job-changes/settings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
        searchId,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    router.refresh()

    toast({
      title: "Your job changes settings have been saved 🎉",
      description: "Make those leads rain!",
    })

    router.push("/dashboard/job-changes/track")
  }

  function displayCheck(property, field) {
    if (formValues.crm === "salesforce") {
      return property.name === field.value?.value
    } else if (formValues.crm === "hubspot") {
      return property.value === field.value
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="mb-8 w-4/5">
            <div className="mb-1">
              <Label>Give a name to your config.</Label>
            </div>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Name"
                      className="w-[400px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <h2 className="text-md mb-1 font-semibold">CRM</h2>
          <p className="mb-6 text-sm text-neutral-500">
            Select the CRM you want to pull accounts from.
          </p>
          <FormField
            control={form.control}
            name="crm"
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col space-y-2">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value)
                      form.setValue("list", null)
                      form.setValue("properties_mapping", [
                        {
                          crm: { label: "", value: "" },
                          datachimp: { label: "", value: "" },
                        },
                        {
                          crm: { label: "", value: "" },
                          datachimp: { label: "", value: "" },
                        },
                        {
                          crm: { label: "", value: "" },
                          datachimp: { label: "", value: "" },
                        },
                        {
                          crm: { label: "", value: "" },
                          datachimp: { label: "", value: "" },
                        },
                      ])
                    }}
                    value={formValues.crm || undefined}
                  >
                    <FormControl>
                      <SelectTrigger className="w-[400px]">
                        <SelectValue placeholder="Select a CRM" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {crmOptions.map((crm) => (
                        <SelectItem
                          key={crm.value}
                          value={crm.value}
                          className="cursor-pointer hover:bg-slate-50"
                        >
                          {crm.content}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          {/* Display when fetching CRM properties */}
          {formValues.crm !== "" && crmLoading ? (
            <>
              <div>
                <Separator className="my-9" />
                <h2 className="text-md mb-1 font-semibold">
                  {formValues.crm === "hubspot"
                    ? "Hubspot list"
                    : "Salesforce list view"}
                </h2>
                <p className="mb-6 text-sm text-neutral-500">
                  {formValues.crm === "hubspot"
                    ? "Select a Hubspot list that contains your target companies."
                    : "Select a Salesforce list view that contains your target accounts."}
                </p>
                <ItemSkeleton className="px-0" />
              </div>
              <div className="mb-32">
                <Separator className="my-9" />
                <div>
                  <h2 className="text-md mb-1 font-semibold">Change type</h2>
                  <p className="text-sm text-neutral-500">
                    Do you want to flag job changes from or to those accounts?
                  </p>
                  <ItemSkeleton className="px-0" />
                </div>
                <Separator className="my-9" />
                <div className="mb-8">
                  <h2 className="text-md mb-1 font-semibold">
                    Properties to map
                  </h2>
                  <p className="text-sm text-neutral-500">
                    You need to map these properties from your CRM to Datachimp:
                    company_name, company_domain, linkedin_company_url
                    (optional), linkedin_company_id (optional)
                  </p>
                </div>
                <ItemSkeleton className="px-0" />
                <ItemSkeleton className="px-0" />
                <ItemSkeleton className="px-0" />
              </div>
            </>
          ) : null}

          {/* Select a companies list in the chosen CRM */}
          {formValues.crm !== "" && crmLists ? (
            <div>
              <Separator className="my-9" />
              <h2 className="text-md mb-1 font-semibold">
                {formValues.crm === "hubspot"
                  ? "Hubspot list"
                  : "Salesforce list view"}
              </h2>
              <p className="mb-6 text-sm text-neutral-500">
                {formValues.crm === "hubspot"
                  ? "Select a Hubspot list that contains your target companies."
                  : "Select a Salesforce list view that contains your target accounts."}
              </p>
              <FormField
                control={form.control}
                name="list"
                render={({ field }) => {
                  return (
                    <FormItem className="flex flex-col space-y-2">
                      <Popover
                        open={openCrmLists}
                        onOpenChange={setOpenCrmLists}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openCrmLists}
                            className="w-[400px] justify-between font-normal"
                          >
                            {field.value ? (
                              <div>
                                <span>
                                  {
                                    crmLists.find(
                                      (list) =>
                                        list.value === field.value?.value
                                    )?.label
                                  }
                                </span>
                                <span className="ml-2 rounded-md bg-blue-100 px-[6px] py-0.5 text-xs text-blue-600">
                                  {
                                    crmLists.find(
                                      (list) =>
                                        list.value === field.value?.value
                                    )?.type
                                  }
                                </span>
                              </div>
                            ) : (
                              "Select a list"
                            )}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[400px] p-0" align="start">
                          <Command className="flex max-h-[300px] flex-col overflow-hidden">
                            <CommandInput
                              placeholder="Search list..."
                              className="sticky top-0 z-10"
                            />
                            <CommandEmpty>No list found.</CommandEmpty>
                            <CommandGroup className="overflow-y-auto">
                              {crmLists.map((list) => (
                                <CommandItem
                                  key={list.value}
                                  onSelect={() => {
                                    field.onChange(list)
                                    setOpenCrmLists(false)
                                  }}
                                  className="cursor-pointer hover:bg-slate-50"
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      field.value?.value === list.value
                                        ? "opacity-100"
                                        : "opacity-0"
                                    )}
                                  />
                                  <span>{list.label}</span>
                                  <span className="ml-2 rounded-md bg-blue-100 px-[6px] py-0.5 text-xs text-blue-600">
                                    {list.type}
                                  </span>
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
            </div>
          ) : null}

          {/* Display after fetching CRM properties */}
          {crmProperties ? (
            <div className="mb-32">
              <Separator className="my-9" />
              <FormField
                control={form.control}
                name="change_type"
                render={({ field }) => {
                  return (
                    <FormItem className="space-y-3">
                      <div className="space-y-1.5">
                        <h2 className="text-md mb-1 font-semibold">
                          Change type
                        </h2>
                        <FormLabel className="mb-6 text-sm font-normal text-neutral-500">
                          Do you want to flag job changes from or to those
                          accounts?
                        </FormLabel>
                      </div>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value as "from" | "to" | "both")
                          }}
                          value={formValues.change_type}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="from" />
                            </FormControl>
                            <FormLabel className="font-normal">From</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="to" />
                            </FormControl>
                            <FormLabel className="font-normal">To</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="both" />
                            </FormControl>
                            <FormLabel className="font-normal">Both</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <Separator className="my-9" />
              <div className="mb-8">
                <h2 className="text-md mb-1 font-semibold">
                  Properties to map
                </h2>
                <p className="text-sm text-neutral-500">
                  You need to map these properties from your Accounts to
                  Datachimp: company_name, company_domain, linkedin_company_url
                  (optional), linkedin_company_id (optional)
                </p>
              </div>

              <div className="mb-2 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)] gap-3">
                <div className="flex">
                  <div className="mr-2 h-4 w-4">
                    {formValues.crm === "hubspot" ? (
                      <HubspotLogo />
                    ) : (
                      <SalesforceLogo />
                    )}
                  </div>
                  <FormLabel>
                    {formValues.crm === "hubspot" ? "Hubspot" : "Salesforce"}
                  </FormLabel>
                </div>
                <div />
                <div className="flex">
                  <div className="mr-2 h-4 w-4">
                    <DatachimpLogo />
                  </div>
                  <FormLabel>Datachimp</FormLabel>
                </div>
              </div>

              {datachimpPropertiesOptions.map((_, index) => (
                <div
                  key={index}
                  className="mb-4 grid grid-cols-[minmax(0px,_1fr)_20px_minmax(0px,_1fr)] items-start gap-3 text-[13px]"
                >
                  <div>
                    <FormField
                      control={form.control}
                      name={`properties_mapping[${index}].crm` as any}
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-col space-y-2">
                            <Popover
                              open={open[index].crm}
                              onOpenChange={() =>
                                setOpen((prevOpen) => {
                                  const newOpen = [...prevOpen]
                                  newOpen[index].crm = !newOpen[index].crm
                                  return newOpen
                                })
                              }
                            >
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    role="combobox"
                                    aria-expanded={open[index].crm}
                                    className={cn(
                                      "justify-between truncate",
                                      !field.value?.value && "text-slate-500"
                                    )}
                                  >
                                    {formValues.properties_mapping[index].crm
                                      ?.value
                                      ? formValues.properties_mapping[index].crm
                                          ?.label
                                      : "Select a key..."}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-[426px] bg-white p-0">
                                <Command>
                                  <CommandInput placeholder="Search matching key..." />
                                  <CommandEmpty>
                                    No matching found.
                                  </CommandEmpty>
                                  <CommandGroup
                                    className="overflow-scroll"
                                    style={{
                                      height:
                                        32 * crmProperties?.length + 8 + "px",
                                      maxHeight: "15rem",
                                    }}
                                  >
                                    {crmProperties.properties.map(
                                      (property) => (
                                        <CommandItem
                                          key={property.value}
                                          onSelect={(value) => {
                                            let matchingKey =
                                              crmProperties.properties.find(
                                                (c) =>
                                                  c.label.toLowerCase() ===
                                                  value
                                              )

                                            if (
                                              formValues.crm === "salesforce"
                                            ) {
                                              matchingKey = {
                                                label: matchingKey.label,
                                                value: matchingKey.name,
                                              }
                                            }

                                            form.setValue(
                                              `properties_mapping[${index}].crm` as any,
                                              matchingKey
                                            )
                                          }}
                                          className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                                        >
                                          <Check
                                            className={cn(
                                              "mr-2 h-4 w-4",
                                              displayCheck(property, field)
                                                ? "opacity-100"
                                                : "opacity-0"
                                            )}
                                          />
                                          {property.label}
                                        </CommandItem>
                                      )
                                    )}
                                  </CommandGroup>
                                </Command>
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                  <div className="h-full">
                    <div className="flex h-10 items-center">
                      <Icons.arrowRight className="h-[20px] w-[20px] text-sm text-slate-300" />
                    </div>
                  </div>
                  <div>
                    <FormField
                      control={form.control}
                      name={`properties_mapping[${index}].datachimp` as any}
                      render={({ field }) => {
                        return (
                          <FormItem className="flex flex-col space-y-2">
                            <Popover
                              open={open[index].datachimp}
                              onOpenChange={() =>
                                setOpen((prevOpen) => {
                                  const newOpen = [...prevOpen]
                                  newOpen[index].datachimp =
                                    !newOpen[index].datachimp
                                  return newOpen
                                })
                              }
                            >
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    role="combobox"
                                    aria-expanded={open[index].datachimp}
                                    className={cn(
                                      "justify-between truncate",
                                      !field.value?.value && "text-slate-500"
                                    )}
                                  >
                                    {formValues.properties_mapping[index]
                                      .datachimp?.value
                                      ? formValues.properties_mapping[index]
                                          .datachimp?.label
                                      : "Select a key..."}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-[426px] bg-white p-0">
                                <Command>
                                  <CommandInput placeholder="Search matching key..." />
                                  <CommandEmpty>
                                    No matching found.
                                  </CommandEmpty>
                                  <CommandGroup
                                    className="overflow-scroll"
                                    style={{
                                      height:
                                        32 *
                                          datachimpPropertiesOptions?.length +
                                        8 +
                                        "px",
                                      maxHeight: "15rem",
                                    }}
                                  >
                                    {datachimpPropertiesOptions.map(
                                      (property, i) => (
                                        <CommandItem
                                          key={i}
                                          onSelect={(value) => {
                                            let matchingKey =
                                              datachimpPropertiesOptions.find(
                                                (c) =>
                                                  c.label.toLowerCase() ===
                                                  value
                                              )
                                            form.setValue(
                                              `properties_mapping[${index}].datachimp` as any,
                                              matchingKey
                                            )
                                          }}
                                          className="transform cursor-pointer transition-all ease-in hover:bg-slate-50"
                                        >
                                          <Check
                                            className={cn(
                                              "mr-2 h-4 w-4",
                                              property.value === field.value
                                                ? "opacity-100"
                                                : "opacity-0"
                                            )}
                                          />
                                          {property.label}
                                        </CommandItem>
                                      )
                                    )}
                                  </CommandGroup>
                                </Command>
                              </PopoverContent>
                            </Popover>
                            <FormMessage className="text-[13px]" />
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                </div>
              ))}

              {shouldDisplaySaveButton ? (
                <Button type="submit" className="mt-2">
                  {isSaving ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  <span>Save</span>
                </Button>
              ) : null}
            </div>
          ) : null}
        </form>
      </Form>
    </>
  )
}
