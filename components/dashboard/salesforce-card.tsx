"use client"

import * as z from "zod"
import useSWR from "swr"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { salesforceSchema } from "@/lib/validations/salesforce"

import { Button } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Separator } from "@/components/ui/separator"
import ObjectCheck from "./salesforce/object-check"
import CompanyCreationChoice from "./salesforce/company-creation-choice"
import ContactCreationChoice from "./salesforce/contact-creation-choice"
import MatchingKey from "@/components/dashboard/salesforce/matching-key"
import ObjectsFields from "@/components/dashboard/salesforce/objects-fields"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>ooter,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { Form } from "@/components/react-hook-form/form"
import { fetcher } from "@/lib/utils"

const isCustomObjectValid = (formValues) =>
  formValues.salesforce_custom_properties_mapping.length &&
  formValues.salesforce_custom_properties_mapping[0]?.datachimp?.value &&
  formValues.salesforce_custom_properties_mapping[0]?.salesforce?.value

const isCompanyCreationValid = (formValues) =>
  formValues.salesforce_company_creation === "yes" &&
  formValues.salesforce_company_properties_mapping.length &&
  formValues.salesforce_company_properties_mapping[0]?.datachimp?.value &&
  formValues.salesforce_company_properties_mapping[0]?.salesforce?.value

const isCompanyNotCreationValid = (formValues) =>
  formValues.salesforce_company_creation === "no"

const isContactCreationValid = (formValues) =>
  formValues.salesforce_contact_creation === "yes" &&
  formValues.salesforce_contact_properties_mapping.length &&
  formValues.salesforce_contact_properties_mapping[0]?.datachimp?.value &&
  formValues.salesforce_contact_properties_mapping[0]?.salesforce?.value

const isContactNotCreationValid = (formValues) =>
  formValues.salesforce_contact_creation === "no"

const getCardDescription = (path) => {
  switch (path) {
    case "new-hires":
      return "new hires"
    case "job-changes":
      return "job changes"
    case "job-offers":
      return "job offers"
  }
}

export default function SalesforceCard({ path, step, setSteps, stepNumber }) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = useState(false)
  const [objectSynced, setObjectSynced] = useState(false)
  const [enrichmentProviders, setEnrichmentProviders] = useState()
  const [customObjectProperties, setCustomObjectProperties] = useState<any>([])

  function checkRequiredProperties(data) {
    const requiredFields = customObjectProperties.requiredProperties

    // If there are no required fields, skip this check
    if (requiredFields.length === 0) {
      return []
    }

    // Find the required fields that are missing in the mapped fields
    const mappedFields = data.map((mapping) => mapping.salesforce.value)
    const missingFields = requiredFields.filter(
      (field) => !mappedFields.includes(field.name)
    )

    return missingFields.map((field) => field.label)
  }

  async function onSubmit(data: z.infer<typeof salesforceSchema>) {
    setIsSaving(true)

    // Check if all required Salesforce fields have been mapped
    const missingFields = checkRequiredProperties(
      data.salesforce_custom_properties_mapping
    )

    if (missingFields.length > 0) {
      setIsSaving(false)
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `Missing required properties on your custom object: ${missingFields.join(
          ", "
        )}`,
      })
    }

    const response = await fetch(`/api/${path}/workflow?key=salesforce`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    setSteps((steps) =>
      steps.map((step) => {
        if (step.type === "salesforce") {
          return { ...step, open: false }
        } else {
          return step
        }
      })
    )

    router.refresh()

    return toast({
      title: "Your Salesforce settings have been saved 🎉",
      description: "You can configure the rest of your workflow.",
    })
  }

  const form = useForm<z.infer<typeof salesforceSchema>>({
    resolver: zodResolver(salesforceSchema),
    defaultValues: {
      salesforce_object_name: "",
      datachimp_salesforce_matching_key: {
        datachimp: { label: "", value: "" },
        salesforce: { label: "", value: "" },
      },
      salesforce_company_creation: "",
      salesforce_contact_creation: "",
      salesforce_custom_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          salesforce: { label: "", value: "", type: "standard" },
        },
      ],
      salesforce_company_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          salesforce: { label: "", value: "", type: "standard" },
        },
      ],
      salesforce_contact_properties_mapping: [
        {
          datachimp: { label: "", value: "", type: "standard" },
          salesforce: { label: "", value: "", type: "standard" },
        },
      ],
    },
  })

  const { data, error, isLoading } = useSWR(
    `/api/${path}/workflow?key=salesforce`,
    fetcher
  )

  const { data: enrichment } = useSWR(
    `/api/${path}/workflow?key=enrichment`,
    fetcher
  )

  const { data: companyProperties } = useSWR(
    `/api/salesforce/properties?object=Account`,
    fetcher
  )

  const { data: contactProperties } = useSWR(
    `/api/salesforce/properties?object=Lead`,
    fetcher
  )

  const { watch } = form
  const formValues = watch()

  const shouldDisplayCompanyCreation =
    isCustomObjectValid(formValues) && objectSynced

  const shouldDisplayContactCreation =
    isCustomObjectValid(formValues) &&
    objectSynced &&
    (isCompanyCreationValid(formValues) ||
      isCompanyNotCreationValid(formValues))

  const shouldDisplaySaveButton = () => {
    if (
      isCustomObjectValid(formValues) &&
      objectSynced &&
      (isCompanyCreationValid(formValues) ||
        isCompanyNotCreationValid(formValues)) &&
      (isContactCreationValid(formValues) ||
        isContactNotCreationValid(formValues))
    ) {
      return true
    }
  }

  // Reload data in the form every time the step is edited
  useEffect(() => {
    if (data?.salesforce_settings) {
      form.reset(JSON.parse(data.salesforce_settings))
    }
  }, [data, step])

  // Check which are the enrichment providers activated
  useEffect(() => {
    if (enrichment?.enrichment_settings) {
      const parsedProviders = JSON.parse(enrichment.enrichment_settings)
      setEnrichmentProviders(parsedProviders)
    }
  }, [enrichment, step])

  if (isLoading) {
    return (
      <CardItem
        path={path}
        isLoading
        setSteps={setSteps}
        stepNumber={stepNumber}
      />
    )
  }

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          path={path}
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div>
                <CardHeader className="p-0">
                  <CardTitle className="text-lg leading-none">
                    Salesforce
                  </CardTitle>
                  <CardDescription>
                    Send {getCardDescription(path)} data to a Salesforce object.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  <ObjectCheck
                    form={form}
                    formValues={formValues}
                    objectSynced={objectSynced}
                    setObjectSynced={setObjectSynced}
                    setProperties={setCustomObjectProperties}
                  />
                  {customObjectProperties?.properties?.length ? (
                    <>
                      <Separator className="my-9" />
                      <MatchingKey form={form} />
                    </>
                  ) : null}

                  {/* Display properties mapping only if key matching has been made on a custom object and object synced */}
                  {objectSynced &&
                  formValues.salesforce_object_name &&
                  formValues.datachimp_salesforce_matching_key?.datachimp
                    ?.value &&
                  formValues.datachimp_salesforce_matching_key?.salesforce
                    ?.value ? (
                    <>
                      <Separator className="my-9" />
                      <ObjectsFields
                        path={path}
                        form={form}
                        title="Map Datachimp keys to your Salesforce's custom object properties."
                        formKey="salesforce_custom_properties_mapping"
                        formValues={formValues}
                        properties={customObjectProperties?.properties}
                        description="Select the Datachimp keys you want to send to Salesforce and the
                        corresponding properties in your custom object."
                      />
                    </>
                  ) : null}

                  {shouldDisplayCompanyCreation ? (
                    <>
                      {/* Ask for company creation */}
                      <Separator className="my-9" />
                      <CompanyCreationChoice
                        form={form}
                        formValues={formValues}
                      />

                      {formValues.salesforce_company_creation === "yes" ? (
                        <>
                          <Separator className="my-9" />
                          {/* Map Salesforce's company properties */}
                          <ObjectsFields
                            form={form}
                            path={path}
                            title="Map Datachimp keys to your Salesforce's account properties."
                            formKey="salesforce_company_properties_mapping"
                            formValues={formValues}
                            properties={companyProperties?.properties}
                            description="Select the Datachimp keys you want to send to Salesforce and the corresponding properties in your Account object."
                          />
                        </>
                      ) : null}
                    </>
                  ) : null}

                  {shouldDisplayContactCreation ? (
                    <>
                      {/* Ask for contact creation */}
                      <Separator className="my-9" />
                      <ContactCreationChoice
                        form={form}
                        formValues={formValues}
                      />

                      {formValues.salesforce_contact_creation === "yes" ? (
                        <>
                          <Separator className="my-9" />
                          {/* Map Salesforce's contact properties */}
                          <ObjectsFields
                            form={form}
                            path={path}
                            title="Map Datachimp keys to your Salesforce's lead properties."
                            formKey="salesforce_contact_properties_mapping"
                            formValues={formValues}
                            properties={contactProperties?.properties}
                            description="Select the Datachimp keys you want to send to Salesforce and the corresponding properties in your Lead object."
                            enrichmentProviders={enrichmentProviders}
                          />
                        </>
                      ) : null}
                    </>
                  ) : null}
                </CardContent>

                {/* Display save button on custom vs standard objects */}
                {shouldDisplaySaveButton() && (
                  <CardFooter className="p-0">
                    <Button type="submit">
                      {isSaving ? (
                        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      <span>Save step</span>
                    </Button>
                  </CardFooter>
                )}
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ path, isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">Salesforce</CardTitle>
            <CardDescription>
              Send {getCardDescription(path)} data to a Salesforce object.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="salesforce" setSteps={setSteps} />
      )}
    </Card>
  )
}
