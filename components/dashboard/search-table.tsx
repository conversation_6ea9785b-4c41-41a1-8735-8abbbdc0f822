"use client"

import * as React from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { Alert } from "@/components/ui/alert"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

export default function SearchTable({ searchesIds, path }) {
  const router = useRouter()
  const { toast } = useToast()

  const [isSaving, setIsSaving] = React.useState(false)
  const [searchId, setSearchId] = React.useState<number>()
  const [salesNavId, setSalesNavId] = React.useState<string>()
  const [showDeleteAlert, setShowDeleteAlert] = React.useState<boolean>(false)
  const [isDeleteLoading, setIsDeleteLoading] = React.useState<boolean>(false)

  async function createNewSearch() {
    setIsSaving(true)

    const response = await fetch(`/api/${path}/search`, {
      method: "POST",
    })

    setIsSaving(false)

    if (!response?.ok) {
      toast({
        title: "Something went wrong.",
        description: "Your new search was not created. Please try again.",
      })
    } else {
      const data = await response.json()

      if (path === "job-changes") {
        router.push(`/dashboard/${path}/track/${data.searchId}`)
      } else {
        router.push(`/dashboard/${path}/search/${data.searchId}`)
      }
    }
  }

  async function deleteSearch() {
    const response = await fetch(
      `/api/${path}/search?id=${searchId}&salesNavId=${salesNavId}`,
      {
        method: "DELETE",
      }
    )

    if (!response?.ok) {
      toast({
        title: "Something went wrong 🚨",
        description: "Your search was not deleted. Please try again.",
      })
    }

    router.refresh()

    return true
  }

  return (
    <div className="md:max-w-4xl">
      <div className="mb-32">
        <div className="flex justify-between">
          <div>
            <h2 className="text-md mb-1 font-semibold">Searches</h2>
            <p className="mb-10 text-sm text-neutral-500">
              Here&apos;s a list of your different searches.
            </p>
          </div>
          {searchesIds.length < 3 ? (
            <Button type="submit" onClick={createNewSearch}>
              {isSaving ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              <span>New Search</span>
            </Button>
          ) : null}
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {searchesIds.map((search, i) => (
              <TableRow key={i}>
                <TableCell className="font-medium">
                  {search.name ? (
                    search.name
                  ) : (
                    <span className="italic text-slate-400">Unnamed</span>
                  )}
                </TableCell>
                <TableCell className="flex justify-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger className="flex h-8 w-8 items-center justify-center rounded-md border bg-white transition-colors hover:bg-slate-50">
                      <Icons.ellipsis className="h-4 w-4" />
                      <span className="sr-only">Open</span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-white">
                      <Link
                        href={`/dashboard/${path}/${
                          path === "job-changes" ? "track" : "search"
                        }/${search.id}`}
                      >
                        <DropdownMenuItem className="flex cursor-pointer items-center focus:bg-slate-100">
                          Edit
                        </DropdownMenuItem>
                      </Link>
                      <DropdownMenuItem
                        className="flex cursor-pointer items-center text-red-600 focus:bg-red-100"
                        onSelect={() => {
                          setShowDeleteAlert(true)
                          setSearchId(search.id)

                          if (path === "new-hires") {
                            setSalesNavId(search.search)
                          }
                        }}
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <Alert open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
          <Alert.Content>
            <Alert.Header>
              <Alert.Title>
                Are you sure you want to delete this search?
              </Alert.Title>
              <Alert.Description>
                This action cannot be undone.
              </Alert.Description>
            </Alert.Header>
            <Alert.Footer>
              <Alert.Cancel>Cancel</Alert.Cancel>
              <Alert.Action
                onClick={async (event) => {
                  event.preventDefault()
                  setIsDeleteLoading(true)

                  const deleted = await deleteSearch()

                  if (deleted) {
                    setIsDeleteLoading(false)
                    setShowDeleteAlert(false)
                    router.refresh()
                  }
                }}
                className="bg-red-600 focus:ring-red-600"
              >
                {isDeleteLoading ? (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Icons.trash className="mr-2 h-4 w-4" />
                )}
                <span>Delete</span>
              </Alert.Action>
            </Alert.Footer>
          </Alert.Content>
        </Alert>
      </div>
    </div>
  )
}
