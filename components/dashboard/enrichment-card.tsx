"use client"

import * as z from "zod"
import useS<PERSON> from "swr"
import { fetcher } from "@/lib/utils"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"

import Image from "next/image"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/icons"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
  CardContent,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormDescription,
} from "@/components/react-hook-form/form"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"

const descriptions = {
  apollo: "Power your end-to-end sales process.",
  prospeo: "Find anyone's email address.",
  lusha: "Reach the right decision makers.",
  dropcontact: "Enrich all your B2B contacts.",
}

const FormSchema = z.object({
  prospeo: z.boolean(),
  apollo: z.boolean(),
  lusha: z.boolean(),
  dropcontact: z.boolean(),
})

export default function EnrichmentCard({
  step,
  steps,
  setSteps,
  path,
  stepNumber,
  connections,
}) {
  const router = useRouter()
  const { toast } = useToast()

  const { data } = useSWR(`/api/${path}/workflow?key=enrichment`, fetcher)

  const [isSaving, setIsSaving] = useState(false)

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      prospeo: false,
      apollo: false,
      lusha: false,
      dropcontact: false,
    },
  })

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsSaving(true)

    const response = await fetch(`/api/${path}/workflow?key=enrichment`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    setSteps((steps) =>
      steps.map((step) => {
        if (step.type === "enrichment") {
          return { ...step, open: false }
        } else {
          return step
        }
      })
    )

    router.refresh()

    return toast({
      title: "Your enrichment settings have been saved 🎉",
      description: "You can configure the rest of your workflow.",
    })
  }

  // Reload data in the form on component mount
  useEffect(() => {
    if (data?.enrichment_settings) {
      form.reset(JSON.parse(data.enrichment_settings))
    }

    return () => form.reset()
  }, [data, step])

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div className="w-[600px]">
                <CardHeader className="p-0">
                  <CardTitle className="text-lg leading-none">
                    Data Enrichment
                  </CardTitle>
                  <CardDescription>
                    Choose which APIs to use to enrich contacts&apos; data.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  <>
                    {connections
                      .find(
                        (connection) => connection.provider === "enrichment"
                      )
                      .keys.map(
                        (
                          provider:
                            | "prospeo"
                            | "apollo"
                            | "lusha"
                            | "dropcontact",
                          i: number
                        ) => (
                          <FormField
                            key={i}
                            control={form.control}
                            name={provider}
                            render={({ field }) => {
                              return (
                                <FormItem className="mb-4 flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel className="flex items-center text-base">
                                      <Image
                                        src={`/images/${provider}.png`}
                                        alt={provider}
                                        className="mr-1 h-4 w-4"
                                        width={100}
                                        height={100}
                                      />
                                      {provider.charAt(0).toUpperCase() +
                                        provider.slice(1).toLowerCase()}
                                    </FormLabel>
                                    <FormDescription className="text-slate-500">
                                      {descriptions[provider]}
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )
                            }}
                          />
                        )
                      )}
                  </>
                </CardContent>
                <CardFooter className="p-0">
                  <Button type="submit">
                    {isSaving ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    <span>Save step</span>
                  </Button>
                </CardFooter>
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">
              Data Enrichment
            </CardTitle>
            <CardDescription>
              Choose which API to use for contact enrichment.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="enrichment" setSteps={setSteps} />
      )}
    </Card>
  )
}
