"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import { SidebarNavItem } from "types"
import { cn } from "@/lib/utils"
import { Icons } from "@/components/icons"

interface DashboardNavProps {
  items: SidebarNavItem[]
}

export function DashboardNav({ items }: DashboardNavProps) {
  const path = usePathname()
  const insightSegment = path?.split("/")[2]

  if (!items?.length) {
    return null
  }

  return (
    <div>
      <Link href="/dashboard/new-hires/results">
        <span className="hidden py-4 font-cal sm:inline-block">datachimp.</span>
      </Link>

      <nav className="mt-6 grid items-start gap-2">
        {items.map((item: any, index) => {
          const itemInsightSegment = item.href?.split("/")[2]
          const Icon: any = Icons[item.icon]
          return (
            <Link key={index} href={item.disabled ? "/dashboard" : item.href}>
              <div
                className={cn(
                  "flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium text-slate-800 hover:bg-slate-200",
                  insightSegment === itemInsightSegment
                    ? "bg-slate-300"
                    : "transparent",
                  item.disabled && "cursor-not-allowed opacity-80"
                )}
              >
                <span className={cn("group flex items-center")}>
                  <Icon className="mr-2 h-4 w-4" />
                  <span>{item.title}</span>
                </span>
              </div>
            </Link>
          )
        })}
      </nav>
    </div>
  )
}
