"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { formatDate } from "@/lib/utils"
import { Icons } from "@/components/icons"

function getItemsName(insight) {
  switch (insight) {
    case "new-hires":
      return "contacts"
    case "job-offers":
      return "job offers"
    case "job-changes":
      return "job changes"
    case "news":
      return "news"
    default:
      return "insights"
  }
}

export default function PostItem({ post, insight }) {
  const { toast } = useToast()
  const [isSaving, setIsSaving] = useState(false)

  async function sendToWebhook() {
    setIsSaving(true)

    const response = await fetch(`/api/${insight}/send-webhook`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        fileUrl: post.link,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const responseText = await response.json()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: responseText.message,
      })
    }

    return toast({
      title: "Your data has been sent 🎉",
      description: "Check it out on your endpoint!",
    })
  }

  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <span className="font-semibold">{formatDate(post.createdAt)}</span>
        <p className="text-sm text-slate-600">{`${post.items} ${getItemsName(
          insight
        )}`}</p>
      </div>
      <div className="flex">
        <div className="mr-6 flex items-center">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <div onClick={sendToWebhook}>
                  {isSaving ? (
                    <Icons.spinner className="h-5 w-5 animate-spin" />
                  ) : (
                    <Icons.webhook className="h-5 w-5" />
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Send to webhook</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="mr-2 flex items-center">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <a href={post.link}>
                  <Icons.download className="h-5 w-5" />
                </a>
              </TooltipTrigger>
              <TooltipContent>
                <p>Download</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </div>
  )
}
