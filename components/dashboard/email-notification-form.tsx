"use client"

import * as z from "zod"
import { useForm } from "react-hook-form"
import { useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

const FormSchema = z.object({
  email_owner_notification: z.enum(["yes", "no"]).optional(),
})

export function EmailNotificationForm({ email }) {
  const { toast } = useToast()

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email_owner_notification: "no",
    },
  })

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const response = await fetch(`/api/${email.path}/email-notification`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        emailNotif: data.email_owner_notification,
      }),
    })

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    let message
    if (data.email_owner_notification === "yes") {
      message = "You'll start receiving email notifications from now on."
    } else {
      message = "You'll stop receiving email notifications from now on."
    }

    return toast({
      title: "Your email settings have been saved 📧",
      description: message,
    })
  }

  const ownerNotificationYes = formValues.email_owner_notification === "yes"

  // Reload data in the form on component mount
  useEffect(() => {
    if (email) {
      const choice = email.status ? "yes" : "no"
      form.reset({ email_owner_notification: choice })
    }
  }, [email])

  return (
    <div>
      <Form {...form}>
        <form>
          <FormField
            control={form.control}
            name="email_owner_notification"
            render={({ field }) => {
              return (
                <FormItem className="space-y-3">
                  <div className="space-y-1.5">
                    <h2 className="text-md mb-1 font-semibold">
                      Email notification
                    </h2>
                    <FormLabel className="mb-6 text-sm font-normal text-neutral-500">
                      Would you like to get an email notification when a new
                      batch of insights comes in?
                    </FormLabel>
                  </div>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value as "yes" | "no")
                        form.handleSubmit(onSubmit)()
                      }}
                      value={ownerNotificationYes ? "yes" : "no"}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </form>
      </Form>
    </div>
  )
}
