"use client"

import * as z from "zod"
import useS<PERSON> from "swr"
import { fetcher } from "@/lib/utils"
import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { Check, ChevronsUpDown } from "lucide-react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/icons"
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
  CardContent,
} from "@/components/ui/card"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/react-hook-form/form"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { WorkflowOperations } from "@/components/dashboard/workflow-operations"

import { cn } from "@/lib/utils"

const FormSchema = z.object({
  teams_channel: z.string().nonempty("Please select a Teams channel."),
  teams_environment: z.string().optional(),
})

export default function TeamsCard({ step, steps, setSteps, path, stepNumber }) {
  const router = useRouter()
  const { toast } = useToast()

  const { data: channels, isLoading: channelIsLoading } = useSWR(
    `/api/teams/channels`,
    fetcher
  )
  const { data } = useSWR(`/api/${path}/workflow?key=teams`, fetcher)

  const [isSaving, setIsSaving] = useState(false)
  const [open, setOpen] = useState(false)

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      teams_channel: "",
      teams_environment: "",
    },
  })

  const { watch } = form
  const formValues = watch()

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsSaving(true)

    const response = await fetch(`/api/${path}/workflow?key=teams`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
      }),
    })

    setIsSaving(false)

    if (!response?.ok) {
      const error = await response.text()
      return toast({
        title: "Uh uh! Something went wrong 🚨",
        description: `${error}`,
      })
    }

    setSteps((steps) =>
      steps.map((step) => {
        if (step.type === "microsoft-teams") {
          return { ...step, open: false }
        } else {
          return step
        }
      })
    )

    router.refresh()

    return toast({
      title: "Your Teams settings have been saved 🎉",
      description: "You can configure the rest of your workflow.",
    })
  }

  // Reload data in the form every time the step is edited
  useEffect(() => {
    if (data?.teams_settings) {
      form.reset(JSON.parse(data.teams_settings))
    }

    return () => form.setValue("teams_channel", "")
  }, [data, step])

  if (channelIsLoading) {
    return <CardItem isLoading setSteps={setSteps} stepNumber={stepNumber} />
  }

  return (
    <div className="relative">
      {!step.open ? (
        <CardItem
          isLoading={false}
          setSteps={setSteps}
          stepNumber={stepNumber}
        />
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Card className="flex p-7">
              <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
                {stepNumber + 1}
              </div>
              <div>
                <CardHeader className="p-0">
                  <CardTitle className="text-md">Teams Notifications</CardTitle>
                  <CardDescription>
                    Choose a Teams channel where you want to send your data.
                  </CardDescription>
                </CardHeader>
                <Separator className="my-9" />
                <CardContent className="mb-7 p-0">
                  <FormField
                    control={form.control}
                    name="teams_channel"
                    render={({ field }) => (
                      <FormItem className="flex flex-col space-y-2">
                        <FormLabel className="w-max">Channel</FormLabel>
                        <Popover open={open} onOpenChange={setOpen}>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                aria-expanded={open}
                                className={cn(
                                  "w-[260px] justify-between text-sm",
                                  !field.value && "text-slate-500"
                                )}
                              >
                                {field.value
                                  ? channels?.channels.find(
                                      (channel) => channel.value === field.value
                                    )?.label
                                  : "Select channel..."}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-[300px] bg-white p-0">
                            <Command>
                              <CommandInput placeholder="Search channel..." />
                              <CommandEmpty>No channel found.</CommandEmpty>
                              <CommandGroup className="h-[19rem] overflow-scroll">
                                {channels?.channels?.map((channel) => (
                                  <CommandItem
                                    key={channel.value}
                                    onSelect={(value) => {
                                      form.setValue(
                                        "teams_channel",
                                        channels?.channels.find(
                                          (c) => c.label.toLowerCase() === value
                                        )?.value
                                      )
                                      form.setValue(
                                        "teams_environment",
                                        channels.teamsId
                                      )
                                    }}
                                    className="cursor-pointer"
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        channel.value === field.value
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                    {channel.label}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter className="p-0">
                  <Button type="submit">
                    {isSaving ? (
                      <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    <span>Save step</span>
                  </Button>
                </CardFooter>
              </div>
            </Card>
          </form>
          <DropdownMenu>
            <DropdownMenuTrigger className="absolute right-4 top-4 rounded-md p-2 transition-colors hover:bg-slate-50">
              <Icons.more className="h-4 w-4" />
              <span className="sr-only">Open</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white">
              <DropdownMenuItem
                className="cursor-pointer text-red-600 focus:bg-red-100"
                onSelect={() =>
                  setSteps((steps) => steps.filter((s) => s.type !== step.type))
                }
              >
                Remove
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </Form>
      )}
    </div>
  )
}

export function CardItem({ isLoading, stepNumber, setSteps }) {
  return (
    <Card className="flex items-center justify-between p-7">
      <div className="flex">
        <div className="mr-4 flex h-5 w-5 items-center justify-center rounded-full bg-slate-100 p-1 text-xs font-medium">
          {stepNumber + 1}
        </div>
        <div>
          <CardHeader className="p-0">
            <CardTitle className="text-lg leading-none">
              Teams Notifications
            </CardTitle>
            <CardDescription>
              Choose a Teams channel where you want to send your data.
            </CardDescription>
          </CardHeader>
        </div>
      </div>
      {isLoading ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <WorkflowOperations stepName="microsoft-teams" setSteps={setSteps} />
      )}
    </Card>
  )
}
