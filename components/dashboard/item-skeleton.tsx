import { Skeleton } from "@/components/ui/skeleton"

import { cn } from "@/lib/utils"

interface ItemSkeletonProps {
  className?: string
}

export default function ItemSkeleton({ className }: ItemSkeletonProps) {
  return (
    <div className={cn("p-4", className)}>
      <div className="space-y-3">
        <Skeleton className="h-5 w-2/5" />
        <Skeleton className="h-4 w-4/5" />
      </div>
    </div>
  )
}
