"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

const TooltipProvider = TooltipPrimitive.Provider

const Tooltip = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root> & {
    children: [React.ReactElement, React.ReactElement]
  }
>(({ children, ...props }, ref) => {
  const [open, setOpen] = React.useState(false)
  const [trigger, content] = React.Children.toArray(children) as [
    React.ReactElement,
    React.ReactElement
  ]

  const modifiedTrigger = React.cloneElement(trigger, {
    ...trigger.props,
    onClick: (e: React.MouseEvent<HTMLElement>) => {
      const isAnchorClick =
        e.target instanceof Element &&
        (e.target.tagName === "A" || e.target.closest("a"))

      if (!isAnchorClick) {
        e.preventDefault()
      }
      e.stopPropagation()
      if (trigger.props.onClick) trigger.props.onClick(e)
    },
    onMouseEnter: (e: React.MouseEvent<HTMLElement>) => {
      setOpen(true)
      if (trigger.props.onMouseEnter) trigger.props.onMouseEnter(e)
    },
    onMouseLeave: (e: React.MouseEvent<HTMLElement>) => {
      setOpen(false)
      if (trigger.props.onMouseLeave) trigger.props.onMouseLeave(e)
    },
    onMouseDown: (e: React.MouseEvent<HTMLElement>) => {
      const isAnchorMouseDown =
        e.target instanceof Element &&
        (e.target.nodeName === "A" || e.target.closest("a"))

      if (!isAnchorMouseDown) {
        e.preventDefault()
      }
      if (trigger.props.onMouseDown) trigger.props.onMouseDown(e)
    },
  })

  const modifiedContent = React.cloneElement(content, {
    ...content.props,
    onPointerDownOutside: (e: PointerEvent) => {
      e.preventDefault()
      if (content.props.onPointerDownOutside)
        content.props.onPointerDownOutside(e)
    },
  })

  return (
    <TooltipPrimitive.Root {...props} open={open} onOpenChange={setOpen}>
      <div ref={ref}>
        {modifiedTrigger}
        {modifiedContent}
      </div>
    </TooltipPrimitive.Root>
  )
})
Tooltip.displayName = TooltipPrimitive.Tooltip.displayName

const TooltipTrigger = TooltipPrimitive.Trigger

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      "z-50 overflow-hidden rounded-md bg-slate-800 px-3 py-1.5 text-sm text-slate-100 shadow-md animate-in fade-in-50 data-[side=bottom]:slide-in-from-top-1 data-[side=left]:slide-in-from-right-1 data-[side=right]:slide-in-from-left-1 data-[side=top]:slide-in-from-bottom-1",
      className
    )}
    {...props}
  />
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }
