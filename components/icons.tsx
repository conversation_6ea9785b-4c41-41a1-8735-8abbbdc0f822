import {
  AlertTriangle,
  ArrowRight,
  Briefcase,
  Building,
  Braces,
  Check,
  CheckCircle2,
  ChevronLeft,
  ChevronRight,
  ChevronsUpDown,
  CircleSlash2,
  Clock,
  Command,
  CreditCard,
  DatabaseZap,
  Eye,
  EyeOff,
  File,
  FileText,
  Github,
  GraduationCap,
  Hash,
  HelpCircle,
  Image,
  Loader2,
  MoreVertical,
  MoreHorizontal,
  MapPin,
  Pizza,
  Plus,
  PlusCircleIcon,
  Settings,
  Trash,
  Twitter,
  User,
  UserPlus,
  X,
  Download,
  Webhook,
  Link,
  PlusCircle,
  MinusCircle,
  ToggleLeft,
  Trash2,
  Type,
  BrainCircuit,
  Info,
  Zap,
  RefreshCw,
  Workflow,
  Newspaper,
} from "lucide-react"
import JobChangesIcon from "@/components/icons/user-change"
// import type { Icon as LucideIcon } from "lucide-react"

// export type Icon = LucideIcon

export const Icons = {
  logo: Command,
  close: X,
  building: Building,
  braces: Braces,
  spinner: Loader2,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  chevronUpDown: ChevronsUpDown,
  enrichment: DatabaseZap,
  eye: Eye,
  eyeOff: EyeOff,
  hash: Hash,
  clock: Clock,
  trash: Trash,
  trash2: Trash2,
  post: FileText,
  page: File,
  media: Image,
  settings: Settings,
  billing: CreditCard,
  link: Link,
  mapPin: MapPin,
  ellipsis: MoreVertical,
  more: MoreHorizontal,
  add: Plus,
  addCircle: PlusCircleIcon,
  warning: AlertTriangle,
  user: User,
  userChange: JobChangesIcon,
  arrowRight: ArrowRight,
  help: HelpCircle,
  pizza: Pizza,
  gitHub: Github,
  graduationCap: GraduationCap,
  text: Type,
  boolean: ToggleLeft,
  twitter: Twitter,
  check: Check,
  check2: CheckCircle2,
  newHired: UserPlus,
  newspaper: Newspaper,
  jobOffers: Briefcase,
  download: Download,
  webhook: Webhook,
  include: PlusCircle,
  exclude: MinusCircle,
  brainCircuit: BrainCircuit,
  info: Info,
  zap: Zap,
  slash: CircleSlash2,
  sync: RefreshCw,
  workflow: Workflow,
}
