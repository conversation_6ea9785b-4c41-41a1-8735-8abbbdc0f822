import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import { WebClient } from "@slack/web-api"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "GET") {
    try {
      if (user?.clientId) {
        const accessToken = await getAccessToken(user.clientId, "slack")
        const web = new WebClient(accessToken)
        let channels: any = []
        let cursor = ""

        while (true) {
          const response: any = await web.conversations.list({
            types: "public_channel,private_channel",
            exclude_archived: true,
            limit: 1000,
            cursor: cursor,
          })

          channels = [...channels, ...response.channels]

          if (
            response.response_metadata &&
            response.response_metadata.next_cursor !== ""
          ) {
            cursor = response.response_metadata.next_cursor
          } else {
            break
          }
        }

        channels = channels.map((channel) => ({
          value: channel.id,
          label: `#${channel.name}`,
        }))

        return res.json({
          channels,
        })
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while retrieving your slack channels, try again.`)
    }
  }
}
