import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import { Client } from "@hubspot/api-client"

interface HubSpotList {
  listId: string
  name: string
  processingType: "MANUAL" | "SNAPSHOT" | "DYNAMIC"
  objectTypeId: string
  createdAt: string
  updatedAt: string
}

interface HubSpotListsResponse {
  offset: number
  hasMore: boolean
  lists: HubSpotList[]
  total: number
}

interface HubSpotErrorResponse {
  status: "error"
  message: string
  correlationId: string
  category: string
}

type HubSpotApiResponse = HubSpotListsResponse | HubSpotErrorResponse

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method Not Allowed" })
  }

  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (!user?.clientId) {
    return res.status(401).json({ message: "Unauthorized: Missing client ID" })
  }

  try {
    const accessToken = await getAccessToken(user?.clientId, "hubspot")
    const hubspotClient = new Client({ accessToken })

    const data = (await getAllLists(hubspotClient)) as HubSpotApiResponse

    // Check if the response is an error response
    if ("status" in data && data.status === "error") {
      return res.status(400).json({
        message: data.message,
        correlationId: data.correlationId,
        category: data.category,
      })
    }

    // Handle successful response
    if ("lists" in data) {
      const companyLists = data.lists.filter(
        (list) => list.objectTypeId === "0-2"
      )
      const lists = companyLists.map((list) => ({
        value: list.listId,
        label: list.name,
        type: list.processingType === "DYNAMIC" ? "Active" : "Static",
      }))

      return res.status(200).json(lists)
    }

    // In case the response does not match either expected type
    return res.status(500).json({ message: "Unexpected response format" })
  } catch (error) {
    console.error("Error fetching lists from HubSpot:", error)
    return res
      .status(500)
      .json({ message: `Internal Server Error: ${error.message}` })
  }
}

async function getAllLists(
  hubspotClient
): Promise<{ lists: HubSpotList[]; total: number } | HubSpotErrorResponse> {
  try {
    let allLists: HubSpotList[] = []
    let offset = 0
    const limit = 20

    do {
      const response = await hubspotClient.apiRequest({
        method: "POST",
        path: `/crm/v3/lists/search`,
        body: {
          processingTypes: ["MANUAL", "SNAPSHOT", "DYNAMIC"],
          objectTypeId: "0-2",
          offset: offset,
          limit: limit,
        },
      })

      const data = (await response.json()) as HubSpotApiResponse

      if (isHubSpotErrorResponse(data)) {
        console.error("Error fetching lists:", data.message)
        return data
      }

      allLists = allLists.concat(data.lists)
      offset += limit

      if (!data.hasMore) {
        break
      }
    } while (true)

    return { lists: allLists, total: allLists.length }
  } catch (error) {
    console.error(error)
    return {
      status: "error",
      message: String(error),
      correlationId: "",
      category: "UNKNOWN",
    }
  }
}

function isHubSpotErrorResponse(
  response: HubSpotApiResponse
): response is HubSpotErrorResponse {
  return "status" in response && response.status === "error"
}
