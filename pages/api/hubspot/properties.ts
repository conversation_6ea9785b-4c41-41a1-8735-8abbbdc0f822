import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import { Client } from "@hubspot/api-client"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method Not Allowed" })
  }

  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (!user?.clientId) {
    return res.status(401).json({ message: "Unauthorized: Missing client ID" })
  }

  try {
    const { object } = req.query

    if (!object) {
      return res
        .status(400)
        .json({ message: "Bad Request: Missing object parameter" })
    }

    const accessToken = await getAccessToken(user.clientId, "hubspot")
    const hubspotClient = new Client({ accessToken })

    const response = await hubspotClient.apiRequest({
      method: "get",
      path: `/properties/v1/${object}/properties/`,
    })

    const json: any = await response.json()

    const result = json
      .map(({ name, label, type, options }) => ({
        value: name,
        label,
        type,
        name,
        options: options?.map(({ value, label }) => ({ value, label })) || [],
      }))
      .filter((el) => !el.label.includes("_"))

    return res.json({ properties: result })
  } catch (error) {
    return res
      .status(400)
      .send(`Error while fetching Hubspot properties: ${error.message}`)
  }
}
