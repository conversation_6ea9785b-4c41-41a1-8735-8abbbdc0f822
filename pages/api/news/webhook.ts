import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { webhookUrl } = req.body

      if (webhookUrl) {
        await db.newsSettings.updateMany({
          where: {
            client_id: user?.clientId,
          },
          data: {
            webhook_url: webhookUrl,
            webhook_status: "enabled",
            updatedAt: getCurrentTimestamp(),
          },
        })

        return res.json({ response: "Webhook created!" })
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while creating the webhook: ${error.message}`)
    }
  }

  if (req.method === "DELETE") {
    try {
      await db.newsSettings.updateMany({
        where: {
          client_id: user?.clientId,
        },
        data: {
          webhook_url: null,
          webhook_status: null,
          updatedAt: getCurrentTimestamp(),
        },
      })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while deleting the webhook: ${error.message}`)
    }

    return res.json({ response: "Webhook deleted!" })
  }

  if (req.method === "PATCH") {
    const { update } = req.body

    try {
      await db.newsSettings.updateMany({
        where: {
          client_id: user?.clientId,
        },
        data: {
          webhook_status: update === "pause" ? "disabled" : "enabled",
          updatedAt: getCurrentTimestamp(),
        },
      })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while updating the webhook: ${error.message}`)
    }

    return res.json({ response: "Webhook updated!" })
  }
}
