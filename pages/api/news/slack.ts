import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { data } = req.body
      const channelsConfig = data.slackMappings
      const slackOwnerNotification = data.slackOwnerNotification

      if (channelsConfig.length) {
        for (let channel of channelsConfig) {
          await db.newsSettings.update({
            where: {
              id: channel.searchId,
            },
            data: {
              slack_settings: JSON.stringify({
                slack_channel: channel.slackChannel,
                slack_owner_notification: slackOwnerNotification,
              }),
              updatedAt: getCurrentTimestamp(),
            },
          })
        }

        return res.json({ response: "Slack configuration created!" })
      }

      return res.json({ response: "No Slack channels configured!" })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while saving Slack configuration: ${error.message}`)
    }
  }

  if (req.method === "GET") {
    try {
      const data = await db.newsSettings.findMany({
        where: {
          client_id: user?.clientId,
          name: {
            not: null,
          },
        },
        select: {
          id: true,
          name: true,
          slack_settings: true,
        },
      })

      return res.json(data)
    } catch (error) {
      return res
        .status(400)
        .send(`Error while fetching Slack settings: ${error.message}`)
    }
  }
}
