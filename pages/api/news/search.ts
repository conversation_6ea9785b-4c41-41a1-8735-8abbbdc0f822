import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const clientId = user?.clientId

      const existingEntries = await db.newsSettings.count({
        where: {
          client_id: clientId,
        },
      })

      if (existingEntries < 3 && clientId) {
        const newSearch = await db.newsSettings.create({
          data: {
            client_id: clientId,
            search_settings: null,
            createdAt: getCurrentTimestamp(),
            updatedAt: getCurrentTimestamp(),
          },
        })

        return res.json({
          response: "New news search created!",
          searchId: newSearch.id,
        })
      }

      return res.status(400).send("Maximum of 3 entries / client exceeded.")
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while creating the news search: ${error.message}`)
    }
  }

  if (req.method === "DELETE") {
    try {
      const { id } = req.query

      await db.newsSettings.delete({
        where: {
          id: Number(id),
        },
      })

      return res.json({ response: "Search has been deleted." })
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while deleting the search: ${error.message}`)
    }
  }
}
