import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"
import { Storage } from "@google-cloud/storage"
import { ExecutionsClient } from "@google-cloud/workflows"

import { db } from "@/lib/db"

const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const storage = new Storage({
  projectId: process.env.GOOGLE_PROJECT_ID,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})
const projectId = process.env.GOOGLE_PROJECT_ID
const location = process.env.GOOGLE_QUEUE_LOCATION
const workflowsClient = new ExecutionsClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { fileContent, data, searchName } = req.body

      const clientId = user?.clientId
      const bucketName = "datachimp-news"
      const fileName = `${clientId}/${searchName
        .toLowerCase()
        .replace(" ", "_")}.csv`

      if (!fileContent) {
        const options = {
          prefix: `${clientId}/`,
          delimiter: "/",
        }

        const [files] = await storage.bucket(bucketName).getFiles(options)

        if (!files.length) {
          return res.json({ response: "No CSV file to process." })
        }

        await storage.bucket(bucketName).file(fileName).delete()
        return res.json({ response: "CSV file has been deleted." })
      }

      // Ensures that the CSV data has the optional columns
      // const updatedData = ensureOptionalColumns(data)

      const bucket = storage.bucket(bucketName)
      const file = bucket.file(fileName)

      const stream = file.createWriteStream({
        metadata: {
          contentType: "text/csv",
        },
      })

      stream.on("error", (err) => {
        console.error(err)
        return res.status(500).json({ error: "Failed to upload the CSV file" })
      })

      stream.on("finish", async () => {
        const link = `https://storage.cloud.google.com/datachimp-news/${clientId}/${searchName
          .toLowerCase()
          .replace(" ", "_")}.csv`

        await db.newsSettings.update({
          where: {
            id: Number(data.searchId),
          },
          data: {
            search_settings: JSON.stringify({ data, link }),
            updatedAt: getCurrentTimestamp(),
          },
        })

        // await invoke_workflow_job_changes_config(user)

        return res.json({ response: "News settings created / updated!" })
      })

      stream.end(Buffer.from(fileContent))
    } catch (error) {
      console.log(error)
      return res.status(400).send(error.message)
    }
  }

  if (req.method === "GET") {
    const { searchName } = req.query

    try {
      const clientId = user?.clientId
      const bucketName = "datachimp-news"
      let fileName

      if (Array.isArray(searchName)) {
        fileName = `${clientId}/${searchName[0]
          .toLowerCase()
          .replace(" ", "_")}.csv`
      } else if (typeof searchName === "string") {
        fileName = `${clientId}/${searchName
          .toLowerCase()
          .replace(" ", "_")}.csv`
      } else {
        throw new Error("Invalid searchName type")
      }

      const bucket = storage.bucket(bucketName)
      const file = bucket.file(fileName)

      res.setHeader("Content-Type", "text/csv")
      res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`)

      const readStream = file.createReadStream()

      readStream.on("error", (err) => {
        console.error(err)
        res.status(500).json({ error: "Failed to get the CSV file." })
      })

      readStream.pipe(res)
    } catch (error) {
      console.log(error)
      return res.status(400).send(error.message)
    }
  }
}

async function invoke_workflow_job_changes_config(user) {
  const workflowId = process.env.WORKFLOW_JOB_CHANGES_CONFIG_NAME
  const workflowName = workflowsClient.workflowPath(
    projectId ?? "",
    location ?? "",
    workflowId ?? ""
  )
  const payload = {
    client_id: user.clientId,
  }

  const request = {
    parent: workflowName,
    execution: {
      argument: JSON.stringify(payload),
    },
  }

  const [operation] = await workflowsClient.createExecution(request)
  console.log(`Execution started: ${operation.name}`)
}

function ensureOptionalColumns(data) {
  const lines = data.split("\n")
  let headers = lines[0].split(",").map((header) => header.trim())

  let needLinkedInCompanyUrl = !headers.includes("linkedin_company_url")
  let needLinkedInCompanyId = !headers.includes("linkedin_company_id")

  if (needLinkedInCompanyUrl) headers.push("linkedin_company_url")
  if (needLinkedInCompanyId) headers.push("linkedin_company_id")

  lines[0] = headers.join(",")

  for (let i = 1; i < lines.length; i++) {
    if (lines[i].trim() === "") continue
    if (needLinkedInCompanyUrl) lines[i] += ","
    if (needLinkedInCompanyId) lines[i] += ","
  }

  return lines.join("\n")
}
