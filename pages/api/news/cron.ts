import { NextApiRequest, NextApiResponse } from "next"
import { google } from "@google-cloud/scheduler/build/protos/protos"
import { CloudSchedulerClient } from "@google-cloud/scheduler"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

const computeCron = (req: NextApiRequest) => {
  const { monthDay, weekDay, hours, minutes, timeOfDay } = req.body

  return `${minutes} ${
    timeOfDay === "AM" && hours === 12
      ? 0
      : timeOfDay === "AM" || hours === 12
      ? hours
      : hours + 12
  } ${monthDay ? monthDay : "*"} * ${weekDay || weekDay === 0 ? weekDay : "*"}`
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const projectId = process.env.GOOGLE_PROJECT_ID
  const credential = JSON.parse(
    Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
  )
  const client = new CloudSchedulerClient({
    projectId: projectId,
    credentials: {
      client_email: credential.client_email,
      private_key: credential.private_key,
    },
  })

  if (req.method === "POST") {
    try {
      const cron = computeCron(req)
      const { timezone } = req.body
      const payload = JSON.stringify({
        data: {
          client_id: session?.user.clientId,
        },
        workflow_name: "",
        async: true,
      })

      const request = {
        parent: `projects/${projectId}/locations/europe-west1`,
        job: {
          name: `projects/${projectId}/locations/europe-west1/jobs/news-${session?.user.clientId}`,
          schedule: cron,
          timeZone: timezone,
          httpTarget: {
            uri: process.env.WORKFLOW_LAUNCHER_URL,
            httpMethod: "POST",
            body: Buffer.from(payload),
            headers: { "Content-Type": "application/json" },
            oidcToken: {
              serviceAccountEmail: process.env.APP_ENGINE_SERVICE_ACCOUNT,
            },
          },
        },
      } as google.cloud.scheduler.v1.ICreateJobRequest

      await client.createJob(request)
    } catch (error) {
      return res
        .status(400)
        .send(`Error while creating the scheduler: ${error.message}`)
    }

    return res.json({ response: "Scheduler created!" })
  }

  if (req.method === "DELETE") {
    try {
      const request = {
        name: `projects/${projectId}/locations/europe-west1/jobs/news-${session?.user.clientId}`,
      }

      await client.deleteJob(request)
    } catch (error) {
      return res
        .status(400)
        .send(`Error while deleting the scheduler: ${error.message}`)
    }

    return res.json({ response: "Scheduler deleted!" })
  }

  if (req.method === "PATCH") {
    const body = req.body

    if (body.update === "pause") {
      try {
        const request = {
          name: `projects/${projectId}/locations/europe-west1/jobs/news-${session?.user.clientId}`,
        }

        await client.pauseJob(request)
      } catch (error) {
        return res
          .status(400)
          .send(`Error while pausing the scheduler: ${error.message}`)
      }
    } else if (body.update === "resume") {
      try {
        const request = {
          name: `projects/${projectId}/locations/europe-west1/jobs/news-${session?.user.clientId}`,
        }

        await client.resumeJob(request)
      } catch (error) {
        return res
          .status(400)
          .send(`Error while resuming the scheduler: ${error.message}`)
      }
    }

    return res.json({ response: "Scheduler updated!" })
  }
}
