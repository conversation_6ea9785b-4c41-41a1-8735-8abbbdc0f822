import { NextApiRequest, NextApiResponse } from "next"
import { Storage } from "@google-cloud/storage"
import { ExecutionsClient } from "@google-cloud/workflows"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

const location = process.env.GOOGLE_QUEUE_LOCATION

const projectId = process.env.GOOGLE_PROJECT_ID
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const storage = new Storage({
  projectId: process.env.GOOGLE_PROJECT_ID,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})
const workflowsClient = new ExecutionsClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      let data = req.body

      const { searchId, name } = req.body
      const clientId = user?.clientId
      const bucketName = "datachimp-news"
      const fileName = `${clientId}/${searchId}/${name
        .toLowerCase()
        .replace(" ", "_")}.csv`

      // Upload CSV file to GCS
      const link = await uploadFile(fileName, bucketName, clientId, data)

      const existingRecords = await db.newsSettings.findMany({
        where: {
          client_id: user?.clientId,
          NOT: {
            id: Number(searchId),
          },
        },
      })

      const fieldsToCopy = [
        "webhook_url",
        "webhook_status",
        "workflow_settings",
        "workflow_status",
        "hubspot_settings",
        "salesforce_settings",
        "slack_settings",
        "pipedrive_settings",
      ]

      const { fileContent, ...dataWithoutFileContent } = data

      // Initialize updateData with the new data
      const updateData = {
        name: data.name,
        search_settings: JSON.stringify({ ...dataWithoutFileContent, link }),
        updatedAt: getCurrentTimestamp(),
      }

      // Function to check if a value is not null or empty
      const isValidValue = (value) => value != null && value !== ""

      // Iterate through the fields we want to copy
      fieldsToCopy.forEach((field) => {
        // Find the first non-null, non-empty value for this field across all existing records
        const validRecord = existingRecords.find((record) =>
          isValidValue(record[field])
        )
        if (validRecord) {
          updateData[field] = validRecord[field]
        }
      })

      // Update the specific record
      await db.newsSettings.update({
        where: {
          id: Number(searchId),
        },
        data: updateData,
      })

      // await invoke_workflow_news_config(user)

      return res.json({ response: "News search created / updated!" })
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while creating the news search: ${error.message}`)
    }
  }
}

// Upload file on GCS in the bucket datachimp-news
async function uploadFile(fileName, bucketName, clientId, data) {
  const { searchId, name, fileContent } = data
  const bucket = storage.bucket(bucketName)
  const file = bucket.file(fileName)

  return new Promise((resolve, reject) => {
    const stream = file.createWriteStream({
      metadata: {
        contentType: "text/csv",
      },
    })

    stream.on("error", (err) => {
      console.error("Failed to upload the CSV file >>>", err)
      reject(false)
    })

    stream.on("finish", async () => {
      const link = `https://storage.cloud.google.com/datachimp-news/${clientId}/${searchId}/${name
        .toLowerCase()
        .replace(" ", "_")}.csv`
      resolve(link)
    })

    stream.end(Buffer.from(fileContent))
  })
}

async function invoke_workflow_news_config(user) {
  const workflowId = process.env.WORKFLOW_NEWS_CONFIG_NAME
  const workflowName = workflowsClient.workflowPath(
    projectId ?? "",
    location ?? "",
    workflowId ?? ""
  )
  const payload = {
    client_id: user.clientId,
  }

  const request = {
    parent: workflowName,
    execution: {
      argument: JSON.stringify(payload),
    },
  }

  const [operation] = await workflowsClient.createExecution(request)
  console.log(`Execution started: ${operation.name}`)
}
