import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"
import { GoogleAuth } from "google-auth-library"

import { db } from "@/lib/db"

const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const auth = new GoogleAuth({
  credentials: credential,
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "GET") {
    const { key } = req.query
    const column = `${key}_settings`

    try {
      if (key) {
        const data = await db.newsSettings.findFirst({
          where: {
            client_id: user?.clientId,
          },
          select: {
            [column]: true,
          },
        })

        return res.json(data)
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while fetching ${key} settings: ${error.message}`)
    }
  }

  if (req.method === "POST") {
    try {
      const { data } = req.body
      const { key } = req.query
      const column = `${key}_settings`

      if (data) {
        const updateData = {
          [column]: JSON.stringify(data),
          updatedAt: getCurrentTimestamp(),
        }

        if (column === "workflow_settings") {
          const types = data.map((item) => item.type)

          if (!types.includes("hubspot")) {
            updateData["hubspot_settings"] = null
          }
          if (!types.includes("slack")) {
            updateData["slack_settings"] = null
          }
          if (!types.includes("salesforce")) {
            updateData["salesforce_settings"] = null
          }
        }

        await db.newsSettings.updateMany({
          where: {
            client_id: user?.clientId,
          },
          data: updateData,
        })

        // On workflow saving, compose it in GCP workflows
        if (column === "workflow_settings") {
          const url =
            "https://europe-west1-datachimp.cloudfunctions.net/workflow_composer_prod"
          const client = await auth.getIdTokenClient(url)
          await client.request({
            url,
            method: "POST",
            data: {
              client_id: user?.clientId,
              insight: "news",
              workflow_settings: data,
            },
            headers: {
              "Content-Type": "application/json",
            },
          })
        }

        return res.json({ response: "Workflow created!" })
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while creating the workflow: ${error.message}`)
    }

    return res
      .status(400)
      .send(`There are no steps specified for this workflow`)
  }

  if (req.method === "DELETE") {
    try {
      await db.newsSettings.updateMany({
        where: {
          client_id: user?.clientId,
        },
        data: {
          workflow_settings: null,
          workflow_status: null,
          updatedAt: getCurrentTimestamp(),
        },
      })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while deleting the workflow: ${error.message}`)
    }

    return res.json({ response: "Workflow deleted!" })
  }

  if (req.method === "PATCH") {
    const { update } = req.body

    try {
      await db.newsSettings.updateMany({
        where: {
          client_id: user?.clientId,
        },
        data: {
          workflow_status: update === "pause" ? "disabled" : "enabled",
          updatedAt: getCurrentTimestamp(),
        },
      })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while updating the workflow status: ${error.message}`)
    }

    return res.json({ response: "Workflow updated!" })
  }
}
