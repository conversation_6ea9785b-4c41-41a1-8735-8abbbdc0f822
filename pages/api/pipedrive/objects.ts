import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import pipedrive from "pipedrive"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { object } = req.body

      if (user?.clientId) {
        const accessToken = await getAccessToken(user?.clientId, "pipedrive")
        const apiClient = new pipedrive.ApiClient()
        apiClient.authentications.oauth2.accessToken = accessToken

        const api = new pipedrive.PersonsApi(apiClient)
        const persons = await api.getPersons()

        // const response = await hubspotClient.apiRequest({
        //   method: "get",
        //   path: `/crm/v3/schemas/`,
        // })
        // const result: any = await response.json()
        // const { results } = result
        // const hubspotObject = results.find((el) => el.name === object)

        // if (hubspotObject) {
        //   const filteredProperties = hubspotObject.properties.filter(
        //     (el) => !el.hidden && !el.modificationMetadata.readOnlyValue
        //   )

        //   return res.json({
        //     success: true,
        //     properties: filteredProperties,
        //   })
        // } else {
        //   return res
        //     .status(400)
        //     .send(
        //       "There's no custom object with this name in your Hubspot workspace."
        //     )
        // }
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while fetching your custom object, try again.`)
    }
  }
}
