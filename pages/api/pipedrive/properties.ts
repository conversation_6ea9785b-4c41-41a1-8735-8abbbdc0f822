import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
const pipedrive = require("pipedrive")

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "GET") {
    try {
      const { object } = req.query

      if (user?.clientId) {
        const accessToken = await getAccessToken(user?.clientId, "pipedrive")

        const apiClient = new pipedrive.ApiClient()
        apiClient.authentications.oauth2.accessToken = accessToken
        const response: any = await getApiResponse(apiClient, object)

        const result = response.data.map(({ key, name, field_type }) => ({
          value: key,
          label: name,
          type: field_type,
          name: key,
        }))

        return res.json({ properties: result })
      }
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while fetching Pipedrive properties: ${error.message}`)
    }
  }
}

async function getApiResponse(apiClient, object) {
  switch (object) {
    case "companies":
      const organizationFieldsApi = new pipedrive.OrganizationFieldsApi(
        apiClient
      )
      const organizationFieldsResponse =
        await organizationFieldsApi.getOrganizationFields()
      return organizationFieldsResponse
    case "contacts":
      const personFieldsApi = new pipedrive.PersonFieldsApi(apiClient)
      const personFieldsResponse = await personFieldsApi.getPersonFields()
      return personFieldsResponse
    default:
      return { success: false, data: [] }
  }
}
