import { NextApiRequest, NextApiResponse } from "next"
import { CloudTasksClient } from "@google-cloud/tasks"
import { ExecutionsClient } from "@google-cloud/workflows"
import { google } from "@google-cloud/tasks/build/protos/protos"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

const project = process.env.GOOGLE_PROJECT_ID
const location = process.env.GOOGLE_QUEUE_LOCATION
const queue = process.env.GOOGLE_QUEUE_NAME

const projectId = process.env.GOOGLE_PROJECT_ID
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const taskClient = new CloudTasksClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})
const workflowsClient = new ExecutionsClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      let data = req.body

      // Check if a boolean search has been created. If one has been created, ensure that it is the sole search criteria being used.
      if (data.titles.length > 1 && is_boolean_search(data.titles)) {
        return res
          .status(400)
          .send(
            "You can't create multiple titles when one of them is a boolean search."
          )
      }

      const salesNavUrl = buildSalesNavUrl(data)
      const salesNavPayload = buildSalesNavPayload(data)

      await getPotentialExistingTask(user, data)
      const created = await createTask(user, data, salesNavUrl, salesNavPayload)

      if (created) {
        // Fetch all existing records for the client
        const existingRecords = await db.newPositionSettings.findMany({
          where: {
            client_id: user?.clientId,
            NOT: {
              id: Number(data.searchId),
            },
          },
        })

        // Define the fields we want to potentially copy
        const fieldsToCopy = [
          "webhook_url",
          "webhook_status",
          "workflow_settings",
          "workflow_status",
          "hubspot_settings",
          "enrichment_settings",
          "salesforce_settings",
          "slack_settings",
          "pipedrive_settings",
        ]

        // Initialize updateData with the new data
        const updateData = {
          name: data.name,
          search_settings: JSON.stringify(data),
          updatedAt: getCurrentTimestamp(),
        }

        // Function to check if a value is not null or empty
        const isValidValue = (value) => value != null && value !== ""

        // Iterate through the fields we want to copy
        fieldsToCopy.forEach((field) => {
          // Find the first non-null, non-empty value for this field across all existing records
          const validRecord = existingRecords.find((record) =>
            isValidValue(record[field])
          )
          if (validRecord) {
            updateData[field] = validRecord[field]
          }
        })

        // Update the specific record
        await db.newPositionSettings.update({
          where: {
            id: Number(data.searchId),
          },
          data: updateData,
        })

        await invoke_workflow_new_hires_config(user)

        return res.json({ response: "Titles search created / updated!" })
      }

      return res
        .status(400)
        .send(`Error while creating / updating the titles search.`)
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while creating the titles search: ${error.message}`)
    }
  }
}

function is_boolean_search(rawTitles) {
  const boolean_operators = ["AND", "OR", "NOT"]
  const queries = rawTitles.map((el) => el.value)

  for (let query of queries) {
    const words = query.split(" ")

    for (let word of words) {
      if (boolean_operators.includes(word)) {
        return true
      }
    }
  }

  return false
}

function sanitizeTitles(data) {
  data.titles = data.titles.map((title) => {
    return {
      label: title.label.replace(/[\'\"]/g, " "),
      value: title.value.replace(/[\'\"]/g, " "),
      excluded: title.excluded,
    }
  })

  return data
}

async function getPotentialExistingTask(user, data) {
  const parent = `projects/${project}/locations/${location}/queues/${queue}`
  const iterable = taskClient.listTasksAsync({ parent })

  for await (const response of iterable) {
    if (response && response.name) {
      const splittedName = response.name.split("/")
      const name = splittedName[splittedName.length - 1]
      const clientId = name.split("_")[0]
      const searchId = name.split("_")[1]

      if (clientId === user.clientId && searchId === data.searchId) {
        await deleteOldTask(response.name)
      }
    }
  }
}

async function deleteOldTask(name: string) {
  await taskClient.deleteTask({ name })
}

async function createTask(user, data, salesNavUrl, salesNavPayload) {
  const now = new Date()
  const nowToSeconds = Math.floor(now.getTime() / 1000)
  const parent = `projects/${project}/locations/${location}/queues/${queue}`

  const payload = {
    search_id: data.searchId,
    client_id: user.clientId,
    client_domain: user.domain,
    sales_nav_url: salesNavUrl,
    sales_nav_payload: salesNavPayload,
  }

  const task = {
    name: `${parent}/tasks/${user.clientId}_${data.searchId}_${nowToSeconds}`,
    httpRequest: {
      httpMethod: "POST",
      url: "https://europe-west1-datachimp.cloudfunctions.net/sales_nav_create_search_prod",
      headers: {
        "Content-Type": "application/json",
      },
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      oidcToken: {
        serviceAccountEmail:
          "<EMAIL>",
      },
    },
  } as google.cloud.tasks.v2.ITask

  const fifteenMinutesLater = new Date(now.getTime() + 15 * 60 * 1000)

  task["scheduleTime"] = {
    seconds: Math.floor(fifteenMinutesLater.getTime() / 1000),
    nanos: (fifteenMinutesLater.getTime() % 1000) * 1e6,
  }

  try {
    const [response] = await taskClient.createTask({ parent, task })
    console.log(`Created task: ${response.name}`)
    console.log("------------------------------------------------------")
    return true
  } catch (error) {
    console.log(`Something went wrong while creating the task: ${error}`)
    return false
  }
}

function buildSalesNavUrl(data) {
  data = sanitizeTitles(data)
  let queryString = "(filters:List("
  queryString += "(type:TITLE,values:List("

  const includedTitles = data.titles.filter((title) => !title.excluded)
  includedTitles.forEach((title) => {
    queryString += `(text:${title.value
      .replaceAll("(", "%28")
      .replaceAll(")", "%29")},selectionType:INCLUDED),`
  })
  const excludedTitles = data.titles.filter((title) => title.excluded)
  excludedTitles.forEach((title) => {
    queryString += `(text:${title.value
      .replaceAll("(", "%28")
      .replaceAll(")", "%29")},selectionType:EXCLUDED),`
  })

  queryString = queryString.slice(0, -1)
  queryString += "),selectedSubFilter:CURRENT),"
  queryString += "(type:REGION,values:List("
  data.countries.forEach((country) => {
    queryString += `(id:${country.value},selectionType:INCLUDED),`
  })

  queryString = queryString.slice(0, -1)
  queryString += ")),"
  queryString += "(type:COMPANY_HEADQUARTERS,values:List("
  data.companyCountries.forEach((country) => {
    queryString += `(id:${country.value},selectionType:INCLUDED),`
  })

  queryString = queryString.slice(0, -1)
  queryString += ")),"
  queryString += "(type:INDUSTRY,values:List("

  const includedIndustries = data.industries.filter((el) => !el.excluded)
  includedIndustries.forEach((industry) => {
    queryString += `(id:${industry.value},selectionType:INCLUDED),`
  })
  const excludedIndustries = data.industries.filter((el) => el.excluded)
  excludedIndustries.forEach((industry) => {
    queryString += `(id:${industry.value},selectionType:EXCLUDED),`
  })

  queryString = queryString.slice(0, -1)
  queryString += ")),"
  queryString += "(type:COMPANY_HEADCOUNT,values:List("
  data.companySizes.forEach((size) => {
    queryString += `(id:${size.value},selectionType:INCLUDED),`
  })
  queryString = queryString.slice(0, -1)
  queryString += "))))"

  const url = `https://www.linkedin.com/sales/search/people?query=${encodeURIComponent(
    queryString
  )}&viewAllFilters=true`

  return url
}

function buildSalesNavPayload(data) {
  data = sanitizeTitles(data)
  const includedTitles = data.titles.filter((title) => !title.excluded)
  const excludedTitles = data.titles.filter((title) => title.excluded)
  const includedIndustries = data.industries.filter((el) => !el.excluded)
  const excludedIndustries = data.industries.filter((el) => el.excluded)

  return {
    searchQuery: {
      leadSearchQuery: {
        filters: [
          {
            type: "REGION",
            values: data.countries.map((country) => ({
              id: country.value,
              text: country.label,
              selectionType: "INCLUDED",
            })),
          },
          {
            type: "COMPANY_HEADQUARTERS",
            values: data.companyCountries.map((country) => ({
              id: country.value,
              text: country.label,
              selectionType: "INCLUDED",
            })),
          },
          {
            type: "TITLE",
            values: includedTitles
              .map((title) => ({
                text: title.value,
                selectionType: "INCLUDED",
              }))
              .concat(
                excludedTitles.map((title) => ({
                  text: title.value,
                  selectionType: "EXCLUDED",
                }))
              ),
            selectedSubFilter: "CURRENT",
          },
          {
            type: "INDUSTRY",
            values: includedIndustries
              .map((industry) => ({
                id: industry.value.toString(),
                text: industry.label,
                selectionType: "INCLUDED",
              }))
              .concat(
                excludedIndustries.map((industry) => ({
                  id: industry.value.toString(),
                  text: industry.label,
                  selectionType: "EXCLUDED",
                }))
              ),
          },
          {
            type: "COMPANY_HEADCOUNT",
            values: data.companySizes.map((companySize) => ({
              id: companySize.value,
              text: companySize.label,
              selectionType: "INCLUDED",
            })),
          },
          {
            type: "LEAD_HIGHLIGHTS",
            values: [
              {
                id: "RPC",
                text: "Changed jobs in last 90 days",
                selectionType: "INCLUDED",
              },
            ],
          },
        ],
      },
    },
  }
}

async function invoke_workflow_new_hires_config(user) {
  const workflowId = process.env.WORKFLOW_NEW_HIRES_CONFIG_NAME
  const workflowName = workflowsClient.workflowPath(
    projectId ?? "",
    location ?? "",
    workflowId ?? ""
  )
  const payload = {
    client_id: user.clientId,
  }

  const request = {
    parent: workflowName,
    execution: {
      argument: JSON.stringify(payload),
    },
  }

  const [operation] = await workflowsClient.createExecution(request)
  console.log(`Execution started: ${operation.name}`)
}
