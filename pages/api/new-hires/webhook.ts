import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    if (
      user?.clientId !== "clxizm16y0000l40c0k4kjivg" &&
      user?.clientId !== "clmeu2ril0000jv0fqp6own4e" &&
      user?.clientId !== "cm1kja51x0000mn0cf3db8j28" &&
      user?.clientId !== "cloint5sj0000l60fwbzj1p52"
    ) {
      try {
        const { webhookUrl } = req.body

        if (webhookUrl) {
          await db.newPositionSettings.updateMany({
            where: {
              client_id: user?.clientId,
            },
            data: {
              webhook_url: webhookUrl,
              webhook_status: "enabled",
              updatedAt: getCurrentTimestamp(),
            },
          })

          return res.json({ response: "Webhook created!" })
        }
      } catch (error) {
        return res
          .status(400)
          .send(`Error while creating the webhook: ${error.message}`)
      }
    } else {
      try {
        const { data } = req.body
        const webhooks = data.webhooks

        if (webhooks.length) {
          for (let webhook of webhooks) {
            await db.newPositionSettings.update({
              where: {
                id: webhook.searchId,
              },
              data: {
                webhook_url: webhook.url,
                webhook_status: "enabled",
                updatedAt: getCurrentTimestamp(),
              },
            })
          }

          return res.json({ response: "Webhook created!" })
        }

        return res.json({ response: "No webhooks sent!" })
      } catch (error) {
        return res
          .status(400)
          .send(`Error while creating the webhook: ${error.message}`)
      }
    }
  }

  if (req.method === "DELETE") {
    try {
      await db.newPositionSettings.updateMany({
        where: {
          client_id: user?.clientId,
        },
        data: {
          webhook_url: null,
          webhook_status: null,
          updatedAt: getCurrentTimestamp(),
        },
      })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while deleting the webhook: ${error.message}`)
    }

    return res.json({ response: "Webhook deleted!" })
  }

  if (req.method === "PATCH") {
    const { update } = req.body

    try {
      await db.newPositionSettings.updateMany({
        where: {
          client_id: user?.clientId,
        },
        data: {
          webhook_status: update === "pause" ? "disabled" : "enabled",
          updatedAt: getCurrentTimestamp(),
        },
      })
    } catch (error) {
      return res
        .status(400)
        .send(`Error while updating the webhook: ${error.message}`)
    }

    return res.json({ response: "Webhook updated!" })
  }

  if (req.method === "GET") {
    if (
      user?.clientId === "clxizm16y0000l40c0k4kjivg" ||
      user?.clientId === "clmeu2ril0000jv0fqp6own4e" ||
      user?.clientId === "cm1kja51x0000mn0cf3db8j28" ||
      user?.clientId === "cloint5sj0000l60fwbzj1p52"
    ) {
      try {
        const data = await db.newPositionSettings.findMany({
          where: {
            client_id: user?.clientId,
            name: {
              not: null,
            },
          },
          select: {
            id: true,
            name: true,
            webhook_url: true,
            webhook_status: true,
          },
        })

        return res.json(data)
      } catch (error) {
        return res
          .status(400)
          .send(`Error while fetching webhook data: ${error.message}`)
      }
    }
  }
}
