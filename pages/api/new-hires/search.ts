import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"
import { CloudTasksClient } from "@google-cloud/tasks"
import { google } from "@google-cloud/tasks/build/protos/protos"

import { db } from "@/lib/db"

const project = process.env.GOOGLE_PROJECT_ID
const location = process.env.GOOGLE_QUEUE_LOCATION
const queue = process.env.GOOGLE_QUEUE_NAME

const projectId = process.env.GOOGLE_PROJECT_ID
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const taskClient = new CloudTasksClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const clientId = user?.clientId

      const existingEntries = await db.newPositionSettings.count({
        where: {
          client_id: clientId,
        },
      })

      if (existingEntries < 3 && clientId) {
        const newSearch = await db.newPositionSettings.create({
          data: {
            client_id: clientId,
            search_settings: null,
            current_batch: 0,
            createdAt: getCurrentTimestamp(),
            updatedAt: getCurrentTimestamp(),
          },
        })

        return res.json({
          response: "New titles search created!",
          searchId: newSearch.id,
        })
      }

      return res.status(400).send("Maximum of 3 entries / client exceeded.")
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while creating the titles search: ${error.message}`)
    }
  }

  if (req.method === "DELETE") {
    try {
      const { id, salesNavId } = req.query
      const created = await createTask(user, id, salesNavId)

      if (created) {
        await db.newPositionSettings.delete({
          where: {
            id: Number(id),
          },
        })

        return res.json({ response: "Search has been deleted." })
      }

      return res.status(400).send("Error while deleting your search.")
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while deleting the search: ${error.message}`)
    }
  }
}

async function createTask(user, id, salesNavId) {
  const now = new Date()
  const nowToSeconds = Math.floor(now.getTime() / 1000)
  const parent = `projects/${project}/locations/${location}/queues/${queue}`

  const payload = {
    search_id: id,
    client_id: user.clientId,
    sales_nav_id: salesNavId === "null" ? null : salesNavId,
  }

  const task = {
    name: `${parent}/tasks/${user.clientId}_${id}_${nowToSeconds}`,
    httpRequest: {
      httpMethod: "POST",
      url: "https://europe-west1-datachimp.cloudfunctions.net/sales_nav_delete_search_prod",
      headers: {
        "Content-Type": "application/json",
      },
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      oidcToken: {
        serviceAccountEmail:
          "<EMAIL>",
      },
    },
  } as google.cloud.tasks.v2.ITask

  try {
    const [response] = await taskClient.createTask({ parent, task })
    console.log(`Created task: ${response.name}`)
    console.log("------------------------------------------------------")
    return true
  } catch (error) {
    console.log(`Something went wrong while creating the task: ${error}`)
    return false
  }
}
