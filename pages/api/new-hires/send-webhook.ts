import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { parse } from "papaparse"

import { db } from "@/lib/db"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    const { fileUrl } = req.body

    if (
      user?.clientId !== "clxizm16y0000l40c0k4kjivg" &&
      user?.clientId !== "clmeu2ril0000jv0fqp6own4e" &&
      user?.clientId !== "cm1kja51x0000mn0cf3db8j28" &&
      user?.clientId !== "cloint5sj0000l60fwbzj1p52"
    ) {
      try {
        const webhook = await db.newPositionSettings.findFirst({
          where: {
            client_id: user?.clientId,
          },
          select: {
            webhook_url: true,
          },
        })

        if (!webhook?.webhook_url) {
          return res.status(400).send({
            message: "You haven't created any webhook destination yet.",
          })
        }

        const webhookUrl = webhook.webhook_url
        const data = await getJsonData(fileUrl)
        await sendDataToWebhookOld(webhookUrl, data)

        return res.json({ response: "Data sent!" })
      } catch (error) {
        return res
          .status(400)
          .send(`Error while sending data to webhook: ${error.message}`)
      }
    } else {
      try {
        const data = await getJsonData(fileUrl)
        const webhooks = await getWebhooks(user)
        await sendDataToWebhook(webhooks, data)

        return res.json({ response: "Data sent!" })
      } catch (error) {
        return res
          .status(400)
          .send(`Error while sending data to webhook: ${error.message}`)
      }
    }
  }
}

async function getJsonData(fileUrl: string) {
  const response = await fetch(fileUrl)
  const text = await response.text()
  const results = parse(text, { header: true })

  return results
}

async function getWebhooks(user) {
  const webhooks = await db.newPositionSettings.findMany({
    where: {
      client_id: user?.clientId,
    },
    select: {
      id: true,
      webhook_url: true,
    },
  })

  return webhooks
}

async function sendDataToWebhook(webhooks, data) {
  for (const row of data.data) {
    const searchId = parseInt(row.search_id)
    const webhook = webhooks.find((wh) => wh.id === searchId)

    if (webhook) {
      try {
        const response = await fetch(webhook.webhook_url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(row),
        })

        if (!response.ok) {
          console.error(
            `Failed to send data for search_id ${searchId}. Status: ${response.status}`
          )
        }
      } catch (error) {
        console.error(`Error sending data for search_id ${searchId}:`, error)
      }
    } else {
      console.warn(`No webhook found for search_id ${searchId}`)
    }
  }
}

async function sendDataToWebhookOld(webhookUrl: string, data) {
  await fetch(webhookUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
}
