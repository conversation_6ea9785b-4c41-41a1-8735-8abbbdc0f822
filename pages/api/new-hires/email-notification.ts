import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"

import { db } from "@/lib/db"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { emailNotif } = req.body

      if (emailNotif) {
        await db.user.update({
          where: {
            id: user?.id,
          },
          data: {
            updatedAt: getCurrentTimestamp(),
            newHiresEmail: emailNotif === "yes" ? true : false,
          },
        })

        return res.json({ response: "Email notification settings saved!" })
      }
    } catch (error) {
      return res
        .status(400)
        .send(
          `Error while saving email notification settings: ${error.message}`
        )
    }
  }
}
