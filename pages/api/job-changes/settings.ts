import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"
import { Storage } from "@google-cloud/storage"
import { ExecutionsClient } from "@google-cloud/workflows"

import { db } from "@/lib/db"

const projectId = process.env.GOOGLE_PROJECT_ID
const location = process.env.GOOGLE_QUEUE_LOCATION
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const storage = new Storage({
  projectId: process.env.GOOGLE_PROJECT_ID,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})
const workflowsClient = new ExecutionsClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { data, searchId } = req.body
      const clientId = user?.clientId

      const existingRecords = await db.jobChangesSettings.findMany({
        where: {
          client_id: user?.clientId,
          NOT: {
            id: Number(searchId),
          },
        },
      })

      // Define the fields we want to potentially copy
      const fieldsToCopy = [
        "webhook_url",
        "webhook_status",
        "workflow_settings",
        "workflow_status",
        "hubspot_settings",
        "enrichment_settings",
        "salesforce_settings",
        "slack_settings",
        "pipedrive_settings",
      ]

      // Initialize updateData with the new data
      const updateData = {
        name: data.name,
        search_settings: JSON.stringify(data),
        updatedAt: getCurrentTimestamp(),
      }

      // Function to check if a value is not null or empty
      const isValidValue = (value) => value != null && value !== ""

      // Iterate through the fields we want to copy
      fieldsToCopy.forEach((field) => {
        // Find the first non-null, non-empty value for this field across all existing records
        const validRecord = existingRecords.find((record) =>
          isValidValue(record[field])
        )
        if (validRecord) {
          updateData[field] = validRecord[field]
        }
      })

      // Update the specific record
      await db.jobChangesSettings.update({
        where: {
          id: Number(searchId),
        },
        data: updateData,
      })

      await deleteCsvFile(clientId, searchId)
      await invoke_workflow_job_changes_config(user)

      return res.json({ response: "Job changes settings created / updated!" })
    } catch (error) {
      console.log(error)
      return res.status(400).send(error.message)
    }
  }
}

async function deleteCsvFile(clientId, searchId) {
  const bucketName = "datachimp-job-changes"
  const fileName = `${clientId}/${searchId}/accounts.csv`

  try {
    const options = {
      prefix: `${clientId}/`,
      delimiter: "/",
    }

    const [files] = await storage.bucket(bucketName).getFiles(options)

    if (!files.length) {
      console.log("No CSV file to delete.")
    }

    await storage.bucket(bucketName).file(fileName).delete()
  } catch (err) {
    console.log(`Can't delete the file ${fileName}, probably don't exist.`)
  }
}

async function invoke_workflow_job_changes_config(user) {
  const workflowId = process.env.WORKFLOW_JOB_CHANGES_CONFIG_NAME
  const workflowName = workflowsClient.workflowPath(
    projectId ?? "",
    location ?? "",
    workflowId ?? ""
  )
  const payload = {
    client_id: user.clientId,
  }

  const request = {
    parent: workflowName,
    execution: {
      argument: JSON.stringify(payload),
    },
  }

  const [operation] = await workflowsClient.createExecution(request)
  console.log(`Execution started: ${operation.name}`)
}
