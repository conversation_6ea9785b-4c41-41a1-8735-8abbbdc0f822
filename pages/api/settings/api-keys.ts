import { NextApiRequest, NextApiResponse } from "next"
import { SecretManagerServiceClient } from "@google-cloud/secret-manager"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

const projectId = process.env.GOOGLE_PROJECT_ID
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const secretClient = new SecretManagerServiceClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { api, key } = req.body

      const parent = `projects/${projectId}`

      const [secret] = await secretClient.createSecret({
        parent: parent,
        secretId: `${api}-${user?.clientId}-secret`,
        secret: {
          replication: {
            automatic: {},
          },
        },
      })

      const [version] = await secretClient.addSecretVersion({
        parent: secret.name,
        payload: {
          data: Buffer.from(key, "utf8"),
        },
      })

      return res
        .status(200)
        .send(`Secret created with version: ${version.name}`)
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while creating the secret: ${error.message}`)
    }
  }

  if (req.method === "DELETE") {
    try {
      const { api } = req.query

      console.log("deleting API >>>", api)

      const secretId = `${api}-${user?.clientId}-secret`
      const name = `projects/${projectId}/secrets/${secretId}`

      await secretClient.deleteSecret({ name })

      return res.status(200).send(`Secret ${secretId} deleted successfully`)
    } catch (error) {
      console.log(error)
      return res
        .status(400)
        .send(`Error while deleting the secret: ${error.message}`)
    }
  }
}
