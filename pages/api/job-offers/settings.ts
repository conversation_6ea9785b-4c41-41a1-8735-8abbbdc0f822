import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getCurrentTimestamp } from "@/lib/utils"
import { ExecutionsClient } from "@google-cloud/workflows"

import { db } from "@/lib/db"

const projectId = process.env.GOOGLE_PROJECT_ID
const location = process.env.GOOGLE_QUEUE_LOCATION
const credential = JSON.parse(
  Buffer.from(process.env.GOOGLE_SERVICE_KEY ?? "", "base64").toString()
)
const workflowsClient = new ExecutionsClient({
  projectId: projectId,
  credentials: {
    client_email: credential.client_email,
    private_key: credential.private_key,
  },
})

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const data = req.body

      // Check if a boolean search has been created.
      if (is_boolean_search(data.titles)) {
        return res
          .status(400)
          .send(
            "Sorry, you can't create a boolean search for job offers insights 💂‍♀️"
          )
      }

      const existingRecords = await db.jobOffersSettings.findMany({
        where: {
          client_id: user?.clientId,
          NOT: {
            id: Number(data.searchId),
          },
        },
      })

      const fieldsToCopy = [
        "webhook_url",
        "webhook_status",
        "workflow_settings",
        "workflow_status",
        "hubspot_settings",
        "salesforce_settings",
        "slack_settings",
        "pipedrive_settings",
      ]

      // Initialize updateData with the new data
      const updateData = {
        name: data.name,
        search_settings: JSON.stringify(data),
        updatedAt: getCurrentTimestamp(),
      }

      // Function to check if a value is not null or empty
      const isValidValue = (value) => value != null && value !== ""

      // Iterate through the fields we want to copy
      fieldsToCopy.forEach((field) => {
        // Find the first non-null, non-empty value for this field across all existing records
        const validRecord = existingRecords.find((record) =>
          isValidValue(record[field])
        )
        if (validRecord) {
          updateData[field] = validRecord[field]
        }
      })

      // Update the specific record
      await db.jobOffersSettings.update({
        where: {
          id: Number(data.searchId),
        },
        data: updateData,
      })

      await invoke_workflow_job_offers_config(user)

      return res.json({ response: "Job offers settings created / updated!" })
    } catch (error) {
      console.log(error)
      return res.status(400).send(error.message)
    }
  }
}

function is_boolean_search(rawTitles) {
  const boolean_operators = ["AND", "OR", "NOT"]
  const queries = rawTitles.map((el) => el.value)

  for (let query of queries) {
    const words = query.split(" ")

    for (let word of words) {
      if (boolean_operators.includes(word)) {
        return true
      }
    }
  }

  return false
}

async function invoke_workflow_job_offers_config(user) {
  const workflowId = process.env.WORKFLOW_JOB_OFFERS_CONFIG_NAME
  const workflowName = workflowsClient.workflowPath(
    projectId ?? "",
    location ?? "",
    workflowId ?? ""
  )
  const payload = {
    client_id: user.clientId,
  }

  const request = {
    parent: workflowName,
    execution: {
      argument: JSON.stringify(payload),
    },
  }

  const [operation] = await workflowsClient.createExecution(request)
  console.log(`Execution started: ${operation.name}`)
}
