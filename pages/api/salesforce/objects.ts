import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import jsforce from "jsforce"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "POST") {
    try {
      const { customObject } = req.body

      if (user?.clientId) {
        const { accessToken, instanceUrl } = await getAccessToken(
          user?.clientId,
          "salesforce"
        )
        const conn = new jsforce.Connection({ instanceUrl, accessToken })

        conn
          .sobject(customObject)
          .describe()
          .then((metadata) => {
            const requiredFields = metadata.fields.filter(
              (field) =>
                !field.nillable &&
                !field.defaultedOnCreate &&
                !field.referenceTo.length
            )

            return res.json({
              success: true,
              properties: metadata.fields,
              requiredProperties: requiredFields,
            })
          })
          .catch((err) => {
            // Handle the error. If it's a NOT_FOUND error, it means the object doesn't exist
            if (err.errorCode === "NOT_FOUND") {
              return res
                .status(400)
                .send(
                  "There's no object with this name in your Salesforce workspace."
                )
            } else {
              console.error("Error : " + err.message)
              return res.status(400).send("Error : " + err.message)
            }
          })
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while fetching your custom object, try again.`)
    }
  }
}
