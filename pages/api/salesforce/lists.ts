import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import jsforce from "jsforce"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method Not Allowed" })
  }

  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (!user?.clientId) {
    return res.status(401).json({ message: "Unauthorized: Missing client ID" })
  }

  try {
    const { accessToken, instanceUrl } = await getAccessToken(
      user?.clientId,
      "salesforce"
    )

    const conn = new jsforce.Connection({ instanceUrl, accessToken })
    const result = await conn.sobject("Account").listviews()

    const lists = result.listviews.map((view) => ({
      value: view.id,
      label: view.label,
      type: "List View",
    }))

    return res.status(200).json(lists)
  } catch (error) {
    console.error("Error fetching list views from Salesforce:", error)
    return res
      .status(500)
      .json({ message: `Internal Server Error: ${error.message}` })
  }
}
