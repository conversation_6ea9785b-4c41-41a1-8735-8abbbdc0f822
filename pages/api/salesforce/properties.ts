import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"
import jsforce from "jsforce"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "GET") {
    try {
      const { object } = req.query

      if (user?.clientId) {
        const { accessToken, instanceUrl } = await getAccessToken(
          user?.clientId,
          "salesforce"
        )

        const conn = new jsforce.Connection({ instanceUrl, accessToken })

        conn
          .sobject(object)
          .describe()
          .then((metadata) => {
            return res.json({
              properties: metadata.fields,
            })
          })
          .catch((err) => {
            // Handle the error. If it's a NOT_FOUND error, it means the object doesn't exist
            if (err.errorCode === "NOT_FOUND") {
              return res
                .status(400)
                .send(
                  "There's no object with this name in your Salesforce workspace."
                )
            } else {
              console.error("Error : " + err.message)
              return res.status(400).send("Error : " + err.message)
            }
          })
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while fetching Hubspot objects: ${error.message}`)
    }
  }
}
