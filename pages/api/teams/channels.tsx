import { NextApiRequest, NextApiResponse } from "next"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { getAccessToken } from "@/lib/utils"

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions)
  const user = session?.user

  if (req.method === "GET") {
    try {
      if (user?.clientId) {
        const accessToken = await getAccessToken(
          user.clientId,
          "microsoft-teams"
        )

        const teamsId = await getTeamsId(accessToken)
        const channels = await getChannels(accessToken, teamsId)

        return res.json({
          teamsId,
          channels,
        })
      }
    } catch (error) {
      return res
        .status(400)
        .send(`Error while retrieving your Teams channels, try again.`)
    }
  }
}

async function getTeamsId(accessToken: string) {
  const response = await fetch(
    "https://graph.microsoft.com/v1.0/me/joinedTeams",
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  const data = await response.json()
  const teamsId = data.value[0].id

  return teamsId
}

async function getChannels(accessToken, teamsId) {
  const response = await fetch(
    `https://graph.microsoft.com/v1.0/teams/${teamsId}/channels`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    }
  )

  const data = await response.json()
  const channels = data.value.map((channel) => ({
    value: channel.id,
    label: `#${channel.displayName}`,
  }))

  return channels
}
