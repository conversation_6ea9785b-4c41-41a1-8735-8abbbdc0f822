"use client"

import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components"
import * as React from "react"

interface DatachimpLoginEmailProps {
  url?: string
}

export const DatachimpLoginEmail = ({ url }: DatachimpLoginEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Sign-in link for Datachimp</Preview>
      <Tailwind>
        <Body className="mx-auto my-auto bg-white font-sans">
          <Container className="mx-auto my-[40px] w-[465px] rounded border border-solid border-[#eaeaea] p-[20px]">
            <Section className="mt-[32px]">
              <Img
                src="https://storage.googleapis.com/datachimp-logos/logo-minimal.svg"
                alt="Datachimp"
                className="mx-auto my-0 h-16 w-16"
              />
            </Section>
            <Heading className="mx-0 my-[30px] p-0 text-center text-[24px] font-normal text-black">
              Log into <strong>Datachimp</strong>
            </Heading>
            <Text className="text-[14px] leading-[24px] text-black">
              Hello 👋,
            </Text>
            <Text className="text-[14px] leading-[24px] text-black">
              Click the link below to sign in to your account.
            </Text>
            <Text className="text-[14px] leading-[24px] text-black">
              This link expires in 24 hours and can only be used once.
            </Text>
            <Section className="mb-[32px] mt-[32px] text-center">
              <Button
                className="rounded bg-[#000000] px-4 py-3 text-center font-semibold text-white no-underline"
                href={url}
              >
                Sign in
              </Button>
            </Section>
            <Hr className="mx-0 my-[26px] w-full border border-solid border-[#eaeaea]" />
            <Text className="text-[12px] leading-[24px] text-[#666666]">
              If you were not expecting this invitation, you can ignore this
              email. If you are concerned about your account&apos;s safety,
              please reply to this email to get in touch with us.
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  )
}

export default DatachimpLoginEmail
