import { NextAuthOptions, Profile } from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import AzureADProvider from "next-auth/providers/azure-ad"
import EmailProvider from "next-auth/providers/email"
import { PrismaAdapter } from "@/lib/adapter"
import { Resend } from "resend"
import DatachimpLoginEmail from "emails/magic-link-email"

import { db } from "@/lib/db"

// Initialize Resend with fallback for build time
const resend = new Resend(process.env.RESEND_API_KEY || "dummy_key_for_build")

interface MyProfile extends Profile {
  hd: string
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db as any),
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    AzureADProvider({
      clientId: process.env.MICROSOFT_CLIENT_ID ?? "",
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET ?? "",
      tenantId: "organizations",
      authorization: { params: { scope: "openid profile user.Read email" } },
    }),
    EmailProvider({
      type: "email",
      sendVerificationRequest: async ({ identifier, url, provider }) => {
        try {
          // Skip email sending during build time
          if (process.env.RESEND_API_KEY === "dummy_key_for_build" || !process.env.RESEND_API_KEY) {
            console.log("Skipping email send during build time")
            return
          }

          await resend.emails.send({
            from: "Datachimp <<EMAIL>>",
            to: [identifier],
            subject: "Datachimp Login",
            react: DatachimpLoginEmail({ url }),
          })
        } catch (error) {
          console.error("Failed to send the verification email:", error)
          throw new Error("Failed to send the verification email.")
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, profile }) {
      const allowedDomains = [
        "datachimp.so",
        "getdatachimp.com",
        "atlar.com",
        "qobra.co",
        "getcargo.io",
        "planhat.com",
        "urbanlinker.com",
        "growthadvisors.fr",
        "tryoctant.io",
        "partoo.fr",
        "rerow.fr",
        "adsteroid.com",
        "adsteroid.fr",
      ]

      let isAllowedToSignIn: boolean

      if (!profile) {
        isAllowedToSignIn = allowedDomains.includes(
          user.email?.split("@")[1] || ""
        )
      } else {
        isAllowedToSignIn = allowedDomains.includes((profile as MyProfile).hd)
      }

      if (isAllowedToSignIn) {
        return true
      } else {
        return false
      }
    },
    async jwt({ token, user }) {
      const dbUser = await db.user.findFirst({
        where: {
          email: token.email,
        },
        include: {
          client: true,
        },
      })

      if (!dbUser) {
        token.id = user.id
        return token
      }

      return {
        id: dbUser.id,
        name: dbUser.name,
        email: dbUser.email,
        picture: dbUser.image,
        clientId: dbUser.client.id,
        domain: dbUser.client.domain,
      }
    },
    async session({ token, session }) {
      const dbUser: any = await db.user.findFirst({
        where: {
          id: token.id,
        },
        include: {
          client: true,
        },
      })

      if (token) {
        session.user.id = token.id
        session.user.name = token.name
        session.user.email = token.email
        session.user.image = token.picture
        session.user.clientId = dbUser.client.id
        session.user.domain = dbUser.client.domain
      }

      return session
    },
  },
}
