import { ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { CronConfig } from "types"
import { cronOptions } from "@/config/cronConfig"
import { DateTime } from "luxon"
import { User } from "@prisma/client"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(input: Date): string {
  const date = new Date(input)
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  })
}

export function absoluteUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_APP_URL}${path}`
}

function computeMonthDay(monthDay: number): string {
  switch (monthDay) {
    case 1 || 21 || 31:
      return `${monthDay}st of the month `
    case 21:
      return `${monthDay}st of the month `
    case 31:
      return `${monthDay}st of the month `
    case 2:
      return `${monthDay}nd of the month `
    case 22:
      return `${monthDay}nd of the month `
    case 3:
      return `${monthDay}rd of the month `
    case 23:
      return `${monthDay}rd of the month `
    default:
      return `${monthDay}th of the month `
  }
}

function computeWeekDay(weekDay: number): string {
  return cronOptions.weekDays.find((day) => parseInt(day.value) === weekDay)
    .content
}

export function computeFrequency(data: CronConfig): string {
  let frequency = "Your request will be executed every "
  if (data.monthDay) {
    frequency += computeMonthDay(data.monthDay)
  } else if (data.weekDay) {
    frequency += computeWeekDay(data.weekDay)
  } else {
    frequency += "day"
  }

  frequency += ` at ${data.hours}:00 ${data.timeOfDay} ⏰`

  return frequency
}

export function getCurrentTimestamp() {
  const date = new Date()
  const timestamp = date.toISOString().slice(0, -1) + "+00"
  const dt = DateTime.fromISO(timestamp)

  return dt.toISO()
}

export async function getAccessToken(
  clientId: User["clientId"],
  integration: string,
  retries = 5
) {
  try {
    const response = await fetch(
      `https://api.nango.dev/connection/${clientId}?provider_config_key=${integration}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${process.env.NANGO_API_KEY}`,
        },
      }
    )

    const data = await response.json()
    const accessToken = data.credentials?.access_token

    if (!accessToken) {
      if (retries > 0) {
        console.log("Retrying...")
        await new Promise((r) => setTimeout(r, 2000))
        return getAccessToken(clientId, integration, retries - 1)
      } else {
        console.log("Max retries exceeded")
        return ""
      }
    }

    if (integration === "salesforce") {
      const instanceUrl = data.connection_config.instance_url
      return { accessToken, instanceUrl }
    }

    return accessToken
  } catch (error) {
    console.log(error)
    if (retries > 0) {
      console.log("Retrying...")
      await new Promise((r) => setTimeout(r, 2000))
      return getAccessToken(clientId, integration, retries - 1)
    } else {
      console.log("Max retries exceeded")
      return ""
    }
  }
}

export async function fetcher(endpoint: string) {
  const response = await fetch(endpoint)
  const json = await response.json()

  if (!response.ok) {
    throw json
  }

  return json
}
