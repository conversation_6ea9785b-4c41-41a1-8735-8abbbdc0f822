import * as z from "zod"

export const hubspotSchema = z.object({
  hubspot_object_choice: z.string({
    required_error:
      "Please choose between the companies object or a custom one.",
  }),
  hubspot_object_name: z.string().optional(),
  datachimp_hubspot_matching_key: z.object({
    datachimp: z.object({
      label: z.string(),
      value: z.string(),
    }),
    hubspot: z.object({
      label: z.string(),
      value: z.string(),
    }),
  }),
  hubspot_company_creation: z.string(),
  hubspot_contact_creation: z.string(),
  hubspot_custom_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      hubspot: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
  hubspot_company_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      hubspot: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
  hubspot_contact_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      hubspot: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
})
