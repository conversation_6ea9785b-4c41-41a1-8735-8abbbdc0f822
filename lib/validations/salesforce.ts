import * as z from "zod"

export const salesforceSchema = z.object({
  salesforce_object_name: z.string().optional(),
  datachimp_salesforce_matching_key: z.object({
    datachimp: z.object({
      label: z.string(),
      value: z.string(),
    }),
    salesforce: z.object({
      label: z.string(),
      value: z.string(),
    }),
  }),
  salesforce_company_creation: z.string(),
  salesforce_contact_creation: z.string(),
  salesforce_custom_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      salesforce: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
  salesforce_company_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      salesforce: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
  salesforce_contact_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      salesforce: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
})
