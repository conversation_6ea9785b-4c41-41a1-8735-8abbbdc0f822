import * as z from "zod"

export const pipedriveSchema = z.object({
  datachimp_pipedrive_matching_key: z.object({
    datachimp: z.object({
      label: z.string(),
      value: z.string(),
    }),
    pipedrive: z.object({
      label: z.string(),
      value: z.string(),
    }),
  }),
  pipedrive_company_creation: z.string(),
  pipedrive_contact_creation: z.string(),
  pipedrive_company_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      pipedrive: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
  pipedrive_contact_properties_mapping: z
    .object({
      datachimp: z.object({
        label: z.union([z.string(), z.number()]),
        value: z.union([z.string(), z.number()]),
        type: z.string().optional(),
        staticValue: z.union([z.string(), z.number()]).optional(),
        staticValueType: z.string().optional(),
      }),
      pipedrive: z.object({
        label: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    })
    .array(),
})
