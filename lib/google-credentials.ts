/**
 * Utility for safely handling Google Cloud credentials
 * Prevents build-time errors when environment variables are not available
 */

export interface GoogleCredentials {
  client_email: string
  private_key: string
}

/**
 * Safely parse Google Service Key from environment variable
 * Returns null if the key is not available or invalid (e.g., during build time)
 */
export function getGoogleCredentials(): GoogleCredentials | null {
  try {
    const serviceKey = process.env.GOOGLE_SERVICE_KEY
    
    // Skip if no key or dummy key
    if (!serviceKey || serviceKey === "eyJkdW1teSI6ICJrZXkifQ==") {
      return null
    }
    
    const credential = JSON.parse(
      Buffer.from(serviceKey, "base64").toString()
    )
    
    // Validate that we have the required fields
    if (!credential.client_email || !credential.private_key) {
      console.warn("Google credentials missing required fields")
      return null
    }
    
    return credential
  } catch (error) {
    console.log("Google Service Key not available or invalid:", error.message)
    return null
  }
}

/**
 * Check if Google credentials are available
 */
export function hasGoogleCredentials(): boolean {
  return getGoogleCredentials() !== null
}

/**
 * Get Google Project ID with fallback
 */
export function getGoogleProjectId(): string | undefined {
  return process.env.GOOGLE_PROJECT_ID || undefined
}
