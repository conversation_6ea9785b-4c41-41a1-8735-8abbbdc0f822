generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String   @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @default(now()) @map("updated_at")
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id              String    @id @default(cuid())
  clientId        String
  name            String?
  email           String?   @unique
  emailVerified   DateTime?
  image           String?
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @map("updated_at")
  jobChangesEmail Boolean?
  jobOffersEmail  Boolean?
  newHiresEmail   Boolean?
  newsEmail       Boolean?
  accounts        Account[]
  sessions        Session[]
  client          Client    @relation(fields: [clientId], references: [id])

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Client {
  id                  String                @id @default(cuid())
  domain              String                @unique
  createdAt           DateTime              @default(now()) @map("created_at")
  updatedAt           DateTime              @default(now()) @map("updated_at")
  cargo               Boolean               @default(false)
  jobChangesSettings  JobChangesSettings[]
  jobOffersSettings   JobOffersSettings[]
  jobOffersBatchProgress    JobOffersBatchProgress[]
  newPositionSettings NewPositionSettings[]
  newsSettings        NewsSettings[]
  users               User[]

  @@map("clients")
}

model Company {
  linkedin_id             String    @id @db.VarChar
  company_name            String?   @db.VarChar
  tagline                 String?   @db.VarChar
  description             String?   @db.VarChar
  domain                  String?   @db.VarChar
  employees_count         Int?
  employees_range         String?   @db.VarChar
  employees_url           String?   @db.VarChar
  headquarters            String?   @db.VarChar
  country                 String?   @db.VarChar
  locations               String?   @db.VarChar
  followers_count         Int?
  industry                String?   @db.VarChar
  specialties             String?   @db.VarChar
  type                    String?   @db.VarChar
  phone                   String?   @db.VarChar
  year_founded            String?   @db.VarChar
  linkedin_company_url    String?   @db.VarChar
  linkedin_job_search_url String?   @db.VarChar
  logo_url                String?   @db.VarChar
  crunchbase_url          String?   @db.VarChar
  last_funding_date       String?   @db.VarChar
  last_funding_investors  String?   @db.VarChar
  last_funding_amount     BigInt?
  last_funding_round      String?   @db.VarChar
  createdAt               DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt               DateTime? @default(now()) @map("updated_at") @db.Timestamptz(6)
  target_type             String?   @db.VarChar
  business_model          String?   @db.VarChar
  saas                    Boolean?

  @@map("companies")
}

model NewPositionSettings {
  client_id           String   @db.VarChar
  search              String?  @db.VarChar
  current_batch       Int?
  last_run_date       String?  @db.VarChar
  webhook_url         String?  @db.VarChar
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)
  search_settings     String?  @db.VarChar
  webhook_status      String?  @db.VarChar
  workflow_settings   String?  @db.VarChar
  workflow_status     String?  @db.VarChar
  hubspot_settings    String?  @db.VarChar
  salesforce_settings String?  @db.VarChar
  slack_settings      String?  @db.VarChar
  enrichment_settings String?  @db.VarChar
  pipedrive_settings  String?  @db.VarChar
  id                  Int      @id @default(autoincrement())
  name                String?  @db.VarChar
  teams_settings      String?  @db.VarChar
  limit_settings      String?  @db.VarChar
  client              Client   @relation(fields: [client_id], references: [id])

  @@map("new_position_settings")
}

model JobOffersSettings {
  client_id           String   @db.VarChar
  search_settings     String?  @db.VarChar
  current_batch       Int?
  webhook_url         String?  @db.VarChar
  webhook_status      String?  @db.VarChar
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)
  workflow_settings   String?  @db.VarChar
  workflow_status     String?  @db.VarChar
  hubspot_settings    String?  @db.VarChar
  salesforce_settings String?  @db.VarChar
  slack_settings      String?  @db.VarChar
  pipedrive_settings  String?  @db.VarChar
  id                  Int      @id @default(autoincrement())
  name                String?  @db.VarChar
  teams_settings      String?  @db.VarChar
  limit_settings      String?  @db.VarChar
  client              Client   @relation(fields: [client_id], references: [id])

  @@map("job_offers_settings")
}

model JobOffersBatchProgress {
  id                  Int      @id @default(autoincrement())
  client_id           String   @db.VarChar
  search_id           Int   
  processed_count     Int      @default(0)
  total_count         Int
  updated_at          DateTime @default(now()) @db.Timestamptz(6)
  client              Client   @relation(fields: [client_id], references: [id])

  @@unique([client_id, search_id])
  @@map("job_offers_batch_progress")
}

model JobChangesSettings {
  client_id           String   @db.VarChar
  search_settings     String?  @db.VarChar
  webhook_url         String?  @db.VarChar
  webhook_status      String?  @db.VarChar
  workflow_settings   String?  @db.VarChar
  workflow_status     String?  @db.VarChar
  hubspot_settings    String?  @db.VarChar
  salesforce_settings String?  @db.VarChar
  slack_settings      String?  @db.VarChar
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)
  enrichment_settings String?  @db.VarChar
  pipedrive_settings  String?  @db.VarChar
  id                  Int      @id @default(autoincrement())
  name                String?  @db.VarChar
  teams_settings      String?  @db.VarChar
  limit_settings      String?  @db.VarChar
  client              Client   @relation(fields: [client_id], references: [id])

  @@map("job_changes_settings")
}

model NewsSettings {
  id                  Int      @id @default(autoincrement())
  client_id           String   @db.VarChar
  name                String?  @db.VarChar
  current_batch       Int?
  search_settings     String?  @db.VarChar
  webhook_url         String?  @db.VarChar
  webhook_status      String?  @db.VarChar
  workflow_settings   String?  @db.VarChar
  workflow_status     String?  @db.VarChar
  hubspot_settings    String?  @db.VarChar
  salesforce_settings String?  @db.VarChar
  slack_settings      String?  @db.VarChar
  teams_settings      String?  @db.VarChar
  pipedrive_settings  String?  @db.VarChar
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)
  limit_settings      String?  @db.VarChar
  client              Client   @relation(fields: [client_id], references: [id])

  @@map("news_settings")
}

model NewJobOffersRaw {
  id                  Int      @id @default(autoincrement())
  title               String   @db.VarChar
  company             String   @db.VarChar
  description         String   @db.VarChar
  location            String   @db.VarChar
  country             String   @db.VarChar
  job_boards          String   @db.VarChar
  extensions          String   @db.VarChar
  datachimp_id        String   @db.VarChar
  created_at          DateTime @default(now()) @db.Timestamptz(6)

  @@map("new_job_offers_raw")
}

model Cookies {
  id        String   @id @default(cuid())
  email     String   @unique
  cookies   String
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)

  @@map("cookies")
}

model CountriesRegions {
  id        String   @id @default(cuid())
  country   String   @unique
  regions   String
  countryCode   String @map("country_code") @default("")
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)

  @@map("countries_regions")
}
