-- CreateTable
CREATE TABLE "job_offers_settings" (
    "client_id" VARCHAR NOT NULL,
    "search_settings" VARCHAR,
    "tools_to_search" VARCHAR,
    "webhook_url" VARCHAR,
    "webhook_status" VARCHAR,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateIndex
CREATE UNIQUE INDEX "job_offers_settings_client_id_key" ON "job_offers_settings"("client_id");

-- AddForeignKey
ALTER TABLE "job_offers_settings" ADD CONSTRAINT "job_offers_settings_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "clients"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
