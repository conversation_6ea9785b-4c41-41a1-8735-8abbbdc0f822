-- CreateTable
CREATE TABLE "job_offers_batch_progress" (
    "id" SERIAL NOT NULL,
    "client_id" VARCHAR NOT NULL,
    "search_id" INTEGER NOT NULL,
    "processed_count" INTEGER NOT NULL DEFAULT 0,
    "total_count" INTEGER NOT NULL,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "job_offers_batch_progress_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "job_offers_batch_progress" ADD CONSTRAINT "job_offers_batch_progress_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "clients"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
