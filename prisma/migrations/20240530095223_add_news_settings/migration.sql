-- CreateTable
CREATE TABLE "news_settings" (
    "id" SERIAL NOT NULL,
    "client_id" VARCHAR NOT NULL,
    "name" <PERSON><PERSON><PERSON><PERSON>,
    "search_settings" VARCHAR,
    "webhook_url" VARCHAR,
    "webhook_status" VARCHAR,
    "workflow_settings" <PERSON><PERSON><PERSON><PERSON>,
    "workflow_status" VA<PERSON><PERSON><PERSON>,
    "hubspot_settings" VARCHAR,
    "salesforce_settings" VARCHAR,
    "slack_settings" VARCHAR,
    "teams_settings" VARCHAR,
    "pipedrive_settings" VARCHAR,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "news_settings_pkey" PRIMARY KEY ("id")
);

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "news_settings" ADD CONSTRAINT "news_settings_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "clients"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
