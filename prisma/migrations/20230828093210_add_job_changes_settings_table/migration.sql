-- CreateTable
CREATE TABLE "job_changes_settings" (
    "client_id" VARCHAR NOT NULL,
    "search_settings" VARCHAR,
    "webhook_url" VARCHAR,
    "webhook_status" VARCHAR,
    "workflow_settings" VARCHAR,
    "workflow_status" <PERSON><PERSON><PERSON>R,
    "hubspot_settings" VA<PERSON>HAR,
    "salesforce_settings" VA<PERSON>HA<PERSON>,
    "slack_settings" VARCHAR,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateIndex
CREATE UNIQUE INDEX "job_changes_settings_client_id_key" ON "job_changes_settings"("client_id");

-- AddForeignKey
ALTER TABLE "job_changes_settings" ADD CONSTRAINT "job_changes_settings_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "clients"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
