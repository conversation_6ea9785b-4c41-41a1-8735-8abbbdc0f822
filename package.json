{"name": "datachimp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "preview": "next build && next start", "vercel-build": "prisma generate && prisma migrate deploy && next build", "postinstall": "prisma generate"}, "dependencies": {"@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@editorjs/code": "^2.7.0", "@editorjs/editorjs": "^2.25.0", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.6.2", "@editorjs/inline-code": "^1.3.1", "@editorjs/link": "^2.4.1", "@editorjs/list": "^1.7.0", "@editorjs/paragraph": "^2.8.0", "@editorjs/table": "^2.0.4", "@google-cloud/bigquery": "^6.2.1", "@google-cloud/scheduler": "^3.2.0", "@google-cloud/secret-manager": "^5.0.1", "@google-cloud/storage": "^6.9.3", "@google-cloud/tasks": "^3.1.1", "@google-cloud/workflows": "^2.3.1", "@hookform/resolvers": "^2.9.10", "@hubspot/api-client": "^9.0.0", "@nangohq/frontend": "^0.20.8", "@next-auth/prisma-adapter": "^1.0.4", "@prisma/client": "^4.11.0", "@radix-ui/react-alert-dialog": "^1.0.2", "@radix-ui/react-avatar": "^1.0.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.1", "@radix-ui/react-popover": "^1.0.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-toggle": "^1.0.0", "@react-email/components": "^0.0.21", "@slack/web-api": "^6.8.1", "@vercel/analytics": "^0.1.3", "@vercel/og": "^0.0.21", "class-variance-authority": "^0.4.0", "classcat": "^5.0.4", "clsx": "^1.2.1", "cmdk": "^0.2.0", "cron-parser": "^4.7.1", "date-fns": "^2.29.3", "downshift": "^7.3.2", "encoding": "^0.1.13", "jsforce": "^1.11.1", "liquidjs": "^10.13.1", "lucide-react": "^0.395.0", "luxon": "^3.3.0", "match-sorter": "^6.3.1", "next": "^14.2.5", "next-auth": "^4.24.7", "node-fetch": "^3.3.0", "papaparse": "^5.3.2", "pipedrive": "^22.5.0", "prop-types": "^15.8.1", "protobufjs": "^7.3.2", "radix-ui": "^1.0.1", "raw-body": "^2.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-editor-js": "^2.1.0", "react-email": "^1.9.5", "react-highlight-words": "^0.20.0", "react-hook-form": "^7.38.0", "react-hot-toast": "^2.4.0", "react-popper": "^2.3.0", "react-textarea-autosize": "^8.3.4", "react-use": "^17.4.0", "request": "^2.88.2", "resend": "^2.0.0", "sharp": "^0.31.1", "shiki": "^0.11.1", "swr": "^2.2.0", "tailwind-merge": "^1.6.2", "tailwindcss-animate": "^1.0.5", "zod": "^3.19.1"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@tailwindcss/typography": "^0.5.7", "@types/node": "^18.11.9", "@types/react": "18.0.15", "@types/react-dom": "18.0.6", "autoprefixer": "^10.4.8", "eslint": "8.21.0", "eslint-config-next": "^14.2.5", "husky": "^8.0.2", "mdast-util-toc": "^6.1.0", "postcss": "^8.4.14", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.13", "pretty-quick": "^3.1.3", "prisma": "^4.11.0", "rehype": "^12.0.1", "rehype-autolink-headings": "^6.1.1", "rehype-pretty-code": "^0.5.0", "rehype-slug": "^5.1.0", "remark": "^14.0.2", "remark-gfm": "^3.0.1", "tailwindcss": "^3.1.7", "typescript": "4.7.4", "unist-util-visit": "^4.1.1"}}