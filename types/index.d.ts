import { Icons } from "@/components/icons"
import { User } from "@prisma/client"
import type { Icon } from "lucide-react"
import { boolean } from "zod"

export type NavItem = {
  title: string
  href: string
  disabled?: boolean
}

export type MainNavItem = NavItem

export type SidebarNavItem = {
  title: string
  disabled?: boolean
  dropdown?: boolean
  external?: boolean
  icon?: keyof typeof Icons
} & (
  | {
      href: string
      items?: never
    }
  | {
      href?: string
      items: NavLink[]
    }
)

export type dashboardHeaderItem = {
  title: String
  path: String
  default: boolean
  insights?: String[]
}

export type CronTabsItem = {
  value: string
  content: string
}

export type CronOptionsItem = {
  value: string
  content: string
}

export type DocsConfig = {
  mainNav: MainNavItem[]
  sidebarNav: SidebarNavItem[]
}

export type DashboardConfig = {
  sidebarNav: SidebarNavItem[]
  dashboardHeader: {
    insights: dashboardHeaderItem[]
    settings: dashboardHeaderItem[]
  }
}

export type CronTabsConfig = CronTabsItem[]

export type CronOptionsConfig = {
  monthDays: CronOptionsItem[]
  weekDays: CronOptionsItem[]
  hours: CronOptionsItem[]
  minutes: CronOptionsItem[]
  timesOfDay: CronOptionsItem[]
}

export type SubscriptionPlan = {
  name: string
  description: string
  stripePriceId: string
}

export type CronConfig = {
  monthDay?: number
  weekDay?: number
  hours?: number
  minutes?: number
  timeOfDay?: string
}
